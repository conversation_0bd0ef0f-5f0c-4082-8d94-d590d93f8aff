import { ExceptionOptionType as __ExceptionOptionType } from "@smithy/smithy-client";
import { TranscribeStreamingServiceException as __BaseException } from "./TranscribeStreamingServiceException";
export interface Entity {
  StartTime?: number | undefined;
  EndTime?: number | undefined;
  Category?: string | undefined;
  Type?: string | undefined;
  Content?: string | undefined;
  Confidence?: number | undefined;
}
export declare const ItemType: {
  readonly PRONUNCIATION: "pronunciation";
  readonly PUNCTUATION: "punctuation";
};
export type ItemType = (typeof ItemType)[keyof typeof ItemType];
export interface Item {
  StartTime?: number | undefined;
  EndTime?: number | undefined;
  Type?: ItemType | undefined;
  Content?: string | undefined;
  VocabularyFilterMatch?: boolean | undefined;
  Speaker?: string | undefined;
  Confidence?: number | undefined;
  Stable?: boolean | undefined;
}
export interface Alternative {
  Transcript?: string | undefined;
  Items?: Item[] | undefined;
  Entities?: Entity[] | undefined;
}
export interface AudioEvent {
  AudioChunk?: Uint8Array | undefined;
}
export declare const ParticipantRole: {
  readonly AGENT: "AGENT";
  readonly CUSTOMER: "CUSTOMER";
};
export type ParticipantRole =
  (typeof ParticipantRole)[keyof typeof ParticipantRole];
export interface ChannelDefinition {
  ChannelId: number | undefined;
  ParticipantRole: ParticipantRole | undefined;
}
export declare const ContentRedactionOutput: {
  readonly REDACTED: "redacted";
  readonly REDACTED_AND_UNREDACTED: "redacted_and_unredacted";
};
export type ContentRedactionOutput =
  (typeof ContentRedactionOutput)[keyof typeof ContentRedactionOutput];
export interface PostCallAnalyticsSettings {
  OutputLocation: string | undefined;
  DataAccessRoleArn: string | undefined;
  ContentRedactionOutput?: ContentRedactionOutput | undefined;
  OutputEncryptionKMSKeyId?: string | undefined;
}
export interface ConfigurationEvent {
  ChannelDefinitions?: ChannelDefinition[] | undefined;
  PostCallAnalyticsSettings?: PostCallAnalyticsSettings | undefined;
}
export type AudioStream =
  | AudioStream.AudioEventMember
  | AudioStream.ConfigurationEventMember
  | AudioStream.$UnknownMember;
export declare namespace AudioStream {
  interface AudioEventMember {
    AudioEvent: AudioEvent;
    ConfigurationEvent?: never;
    $unknown?: never;
  }
  interface ConfigurationEventMember {
    AudioEvent?: never;
    ConfigurationEvent: ConfigurationEvent;
    $unknown?: never;
  }
  interface $UnknownMember {
    AudioEvent?: never;
    ConfigurationEvent?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    AudioEvent: (value: AudioEvent) => T;
    ConfigurationEvent: (value: ConfigurationEvent) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: AudioStream, visitor: Visitor<T>) => T;
}
export declare class BadRequestException extends __BaseException {
  readonly name: "BadRequestException";
  readonly $fault: "client";
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<BadRequestException, __BaseException>
  );
}
export interface CallAnalyticsEntity {
  BeginOffsetMillis?: number | undefined;
  EndOffsetMillis?: number | undefined;
  Category?: string | undefined;
  Type?: string | undefined;
  Content?: string | undefined;
  Confidence?: number | undefined;
}
export interface CallAnalyticsItem {
  BeginOffsetMillis?: number | undefined;
  EndOffsetMillis?: number | undefined;
  Type?: ItemType | undefined;
  Content?: string | undefined;
  Confidence?: number | undefined;
  VocabularyFilterMatch?: boolean | undefined;
  Stable?: boolean | undefined;
}
export declare const CallAnalyticsLanguageCode: {
  readonly DE_DE: "de-DE";
  readonly EN_AU: "en-AU";
  readonly EN_GB: "en-GB";
  readonly EN_US: "en-US";
  readonly ES_US: "es-US";
  readonly FR_CA: "fr-CA";
  readonly FR_FR: "fr-FR";
  readonly IT_IT: "it-IT";
  readonly PT_BR: "pt-BR";
};
export type CallAnalyticsLanguageCode =
  (typeof CallAnalyticsLanguageCode)[keyof typeof CallAnalyticsLanguageCode];
export interface TimestampRange {
  BeginOffsetMillis?: number | undefined;
  EndOffsetMillis?: number | undefined;
}
export interface PointsOfInterest {
  TimestampRanges?: TimestampRange[] | undefined;
}
export interface CategoryEvent {
  MatchedCategories?: string[] | undefined;
  MatchedDetails?: Record<string, PointsOfInterest> | undefined;
}
export declare class ConflictException extends __BaseException {
  readonly name: "ConflictException";
  readonly $fault: "client";
  Message?: string | undefined;
  constructor(opts: __ExceptionOptionType<ConflictException, __BaseException>);
}
export declare class InternalFailureException extends __BaseException {
  readonly name: "InternalFailureException";
  readonly $fault: "server";
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<InternalFailureException, __BaseException>
  );
}
export declare class LimitExceededException extends __BaseException {
  readonly name: "LimitExceededException";
  readonly $fault: "client";
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<LimitExceededException, __BaseException>
  );
}
export declare class ServiceUnavailableException extends __BaseException {
  readonly name: "ServiceUnavailableException";
  readonly $fault: "server";
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<ServiceUnavailableException, __BaseException>
  );
}
export interface CharacterOffsets {
  Begin?: number | undefined;
  End?: number | undefined;
}
export interface IssueDetected {
  CharacterOffsets?: CharacterOffsets | undefined;
}
export declare const Sentiment: {
  readonly MIXED: "MIXED";
  readonly NEGATIVE: "NEGATIVE";
  readonly NEUTRAL: "NEUTRAL";
  readonly POSITIVE: "POSITIVE";
};
export type Sentiment = (typeof Sentiment)[keyof typeof Sentiment];
export interface UtteranceEvent {
  UtteranceId?: string | undefined;
  IsPartial?: boolean | undefined;
  ParticipantRole?: ParticipantRole | undefined;
  BeginOffsetMillis?: number | undefined;
  EndOffsetMillis?: number | undefined;
  Transcript?: string | undefined;
  Items?: CallAnalyticsItem[] | undefined;
  Entities?: CallAnalyticsEntity[] | undefined;
  Sentiment?: Sentiment | undefined;
  IssuesDetected?: IssueDetected[] | undefined;
}
export type CallAnalyticsTranscriptResultStream =
  | CallAnalyticsTranscriptResultStream.BadRequestExceptionMember
  | CallAnalyticsTranscriptResultStream.CategoryEventMember
  | CallAnalyticsTranscriptResultStream.ConflictExceptionMember
  | CallAnalyticsTranscriptResultStream.InternalFailureExceptionMember
  | CallAnalyticsTranscriptResultStream.LimitExceededExceptionMember
  | CallAnalyticsTranscriptResultStream.ServiceUnavailableExceptionMember
  | CallAnalyticsTranscriptResultStream.UtteranceEventMember
  | CallAnalyticsTranscriptResultStream.$UnknownMember;
export declare namespace CallAnalyticsTranscriptResultStream {
  interface UtteranceEventMember {
    UtteranceEvent: UtteranceEvent;
    CategoryEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface CategoryEventMember {
    UtteranceEvent?: never;
    CategoryEvent: CategoryEvent;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface BadRequestExceptionMember {
    UtteranceEvent?: never;
    CategoryEvent?: never;
    BadRequestException: BadRequestException;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface LimitExceededExceptionMember {
    UtteranceEvent?: never;
    CategoryEvent?: never;
    BadRequestException?: never;
    LimitExceededException: LimitExceededException;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface InternalFailureExceptionMember {
    UtteranceEvent?: never;
    CategoryEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException: InternalFailureException;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface ConflictExceptionMember {
    UtteranceEvent?: never;
    CategoryEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException: ConflictException;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface ServiceUnavailableExceptionMember {
    UtteranceEvent?: never;
    CategoryEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException: ServiceUnavailableException;
    $unknown?: never;
  }
  interface $UnknownMember {
    UtteranceEvent?: never;
    CategoryEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    UtteranceEvent: (value: UtteranceEvent) => T;
    CategoryEvent: (value: CategoryEvent) => T;
    BadRequestException: (value: BadRequestException) => T;
    LimitExceededException: (value: LimitExceededException) => T;
    InternalFailureException: (value: InternalFailureException) => T;
    ConflictException: (value: ConflictException) => T;
    ServiceUnavailableException: (value: ServiceUnavailableException) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: CallAnalyticsTranscriptResultStream,
    visitor: Visitor<T>
  ) => T;
}
export declare const ClinicalNoteGenerationStatus: {
  readonly COMPLETED: "COMPLETED";
  readonly FAILED: "FAILED";
  readonly IN_PROGRESS: "IN_PROGRESS";
};
export type ClinicalNoteGenerationStatus =
  (typeof ClinicalNoteGenerationStatus)[keyof typeof ClinicalNoteGenerationStatus];
export interface ClinicalNoteGenerationResult {
  ClinicalNoteOutputLocation?: string | undefined;
  TranscriptOutputLocation?: string | undefined;
  Status?: ClinicalNoteGenerationStatus | undefined;
  FailureReason?: string | undefined;
}
export declare const MedicalScribeNoteTemplate: {
  readonly BEHAVIORAL_SOAP: "BEHAVIORAL_SOAP";
  readonly BIRP: "BIRP";
  readonly DAP: "DAP";
  readonly GIRPP: "GIRPP";
  readonly HISTORY_AND_PHYSICAL: "HISTORY_AND_PHYSICAL";
  readonly PHYSICAL_SOAP: "PHYSICAL_SOAP";
  readonly SIRP: "SIRP";
};
export type MedicalScribeNoteTemplate =
  (typeof MedicalScribeNoteTemplate)[keyof typeof MedicalScribeNoteTemplate];
export interface ClinicalNoteGenerationSettings {
  OutputBucketName: string | undefined;
  NoteTemplate?: MedicalScribeNoteTemplate | undefined;
}
export declare const ContentIdentificationType: {
  readonly PII: "PII";
};
export type ContentIdentificationType =
  (typeof ContentIdentificationType)[keyof typeof ContentIdentificationType];
export declare const ContentRedactionType: {
  readonly PII: "PII";
};
export type ContentRedactionType =
  (typeof ContentRedactionType)[keyof typeof ContentRedactionType];
export interface GetMedicalScribeStreamRequest {
  SessionId: string | undefined;
}
export declare const MedicalScribeParticipantRole: {
  readonly CLINICIAN: "CLINICIAN";
  readonly PATIENT: "PATIENT";
};
export type MedicalScribeParticipantRole =
  (typeof MedicalScribeParticipantRole)[keyof typeof MedicalScribeParticipantRole];
export interface MedicalScribeChannelDefinition {
  ChannelId: number | undefined;
  ParticipantRole: MedicalScribeParticipantRole | undefined;
}
export interface MedicalScribeEncryptionSettings {
  KmsEncryptionContext?: Record<string, string> | undefined;
  KmsKeyId: string | undefined;
}
export declare const MedicalScribeLanguageCode: {
  readonly EN_US: "en-US";
};
export type MedicalScribeLanguageCode =
  (typeof MedicalScribeLanguageCode)[keyof typeof MedicalScribeLanguageCode];
export declare const MedicalScribeMediaEncoding: {
  readonly FLAC: "flac";
  readonly OGG_OPUS: "ogg-opus";
  readonly PCM: "pcm";
};
export type MedicalScribeMediaEncoding =
  (typeof MedicalScribeMediaEncoding)[keyof typeof MedicalScribeMediaEncoding];
export interface MedicalScribePostStreamAnalyticsResult {
  ClinicalNoteGenerationResult?: ClinicalNoteGenerationResult | undefined;
}
export interface MedicalScribePostStreamAnalyticsSettings {
  ClinicalNoteGenerationSettings: ClinicalNoteGenerationSettings | undefined;
}
export declare const MedicalScribeStreamStatus: {
  readonly COMPLETED: "COMPLETED";
  readonly FAILED: "FAILED";
  readonly IN_PROGRESS: "IN_PROGRESS";
  readonly PAUSED: "PAUSED";
};
export type MedicalScribeStreamStatus =
  (typeof MedicalScribeStreamStatus)[keyof typeof MedicalScribeStreamStatus];
export declare const MedicalScribeVocabularyFilterMethod: {
  readonly MASK: "mask";
  readonly REMOVE: "remove";
  readonly TAG: "tag";
};
export type MedicalScribeVocabularyFilterMethod =
  (typeof MedicalScribeVocabularyFilterMethod)[keyof typeof MedicalScribeVocabularyFilterMethod];
export interface MedicalScribeStreamDetails {
  SessionId?: string | undefined;
  StreamCreatedAt?: Date | undefined;
  StreamEndedAt?: Date | undefined;
  LanguageCode?: MedicalScribeLanguageCode | undefined;
  MediaSampleRateHertz?: number | undefined;
  MediaEncoding?: MedicalScribeMediaEncoding | undefined;
  VocabularyName?: string | undefined;
  VocabularyFilterName?: string | undefined;
  VocabularyFilterMethod?: MedicalScribeVocabularyFilterMethod | undefined;
  ResourceAccessRoleArn?: string | undefined;
  ChannelDefinitions?: MedicalScribeChannelDefinition[] | undefined;
  EncryptionSettings?: MedicalScribeEncryptionSettings | undefined;
  StreamStatus?: MedicalScribeStreamStatus | undefined;
  PostStreamAnalyticsSettings?:
    | MedicalScribePostStreamAnalyticsSettings
    | undefined;
  PostStreamAnalyticsResult?:
    | MedicalScribePostStreamAnalyticsResult
    | undefined;
}
export interface GetMedicalScribeStreamResponse {
  MedicalScribeStreamDetails?: MedicalScribeStreamDetails | undefined;
}
export declare class ResourceNotFoundException extends __BaseException {
  readonly name: "ResourceNotFoundException";
  readonly $fault: "client";
  Message?: string | undefined;
  constructor(
    opts: __ExceptionOptionType<ResourceNotFoundException, __BaseException>
  );
}
export declare const LanguageCode: {
  readonly AF_ZA: "af-ZA";
  readonly AR_AE: "ar-AE";
  readonly AR_SA: "ar-SA";
  readonly CA_ES: "ca-ES";
  readonly CS_CZ: "cs-CZ";
  readonly DA_DK: "da-DK";
  readonly DE_CH: "de-CH";
  readonly DE_DE: "de-DE";
  readonly EL_GR: "el-GR";
  readonly EN_AB: "en-AB";
  readonly EN_AU: "en-AU";
  readonly EN_GB: "en-GB";
  readonly EN_IE: "en-IE";
  readonly EN_IN: "en-IN";
  readonly EN_NZ: "en-NZ";
  readonly EN_US: "en-US";
  readonly EN_WL: "en-WL";
  readonly EN_ZA: "en-ZA";
  readonly ES_ES: "es-ES";
  readonly ES_US: "es-US";
  readonly EU_ES: "eu-ES";
  readonly FA_IR: "fa-IR";
  readonly FI_FI: "fi-FI";
  readonly FR_CA: "fr-CA";
  readonly FR_FR: "fr-FR";
  readonly GL_ES: "gl-ES";
  readonly HE_IL: "he-IL";
  readonly HI_IN: "hi-IN";
  readonly HR_HR: "hr-HR";
  readonly ID_ID: "id-ID";
  readonly IT_IT: "it-IT";
  readonly JA_JP: "ja-JP";
  readonly KO_KR: "ko-KR";
  readonly LV_LV: "lv-LV";
  readonly MS_MY: "ms-MY";
  readonly NL_NL: "nl-NL";
  readonly NO_NO: "no-NO";
  readonly PL_PL: "pl-PL";
  readonly PT_BR: "pt-BR";
  readonly PT_PT: "pt-PT";
  readonly RO_RO: "ro-RO";
  readonly RU_RU: "ru-RU";
  readonly SK_SK: "sk-SK";
  readonly SO_SO: "so-SO";
  readonly SR_RS: "sr-RS";
  readonly SV_SE: "sv-SE";
  readonly TH_TH: "th-TH";
  readonly TL_PH: "tl-PH";
  readonly UK_UA: "uk-UA";
  readonly VI_VN: "vi-VN";
  readonly ZH_CN: "zh-CN";
  readonly ZH_HK: "zh-HK";
  readonly ZH_TW: "zh-TW";
  readonly ZU_ZA: "zu-ZA";
};
export type LanguageCode = (typeof LanguageCode)[keyof typeof LanguageCode];
export interface LanguageWithScore {
  LanguageCode?: LanguageCode | undefined;
  Score?: number | undefined;
}
export declare const MediaEncoding: {
  readonly FLAC: "flac";
  readonly OGG_OPUS: "ogg-opus";
  readonly PCM: "pcm";
};
export type MediaEncoding = (typeof MediaEncoding)[keyof typeof MediaEncoding];
export interface MedicalEntity {
  StartTime?: number | undefined;
  EndTime?: number | undefined;
  Category?: string | undefined;
  Content?: string | undefined;
  Confidence?: number | undefined;
}
export interface MedicalItem {
  StartTime?: number | undefined;
  EndTime?: number | undefined;
  Type?: ItemType | undefined;
  Content?: string | undefined;
  Confidence?: number | undefined;
  Speaker?: string | undefined;
}
export interface MedicalAlternative {
  Transcript?: string | undefined;
  Items?: MedicalItem[] | undefined;
  Entities?: MedicalEntity[] | undefined;
}
export declare const MedicalContentIdentificationType: {
  readonly PHI: "PHI";
};
export type MedicalContentIdentificationType =
  (typeof MedicalContentIdentificationType)[keyof typeof MedicalContentIdentificationType];
export interface MedicalResult {
  ResultId?: string | undefined;
  StartTime?: number | undefined;
  EndTime?: number | undefined;
  IsPartial?: boolean | undefined;
  Alternatives?: MedicalAlternative[] | undefined;
  ChannelId?: string | undefined;
}
export interface MedicalScribeAudioEvent {
  AudioChunk: Uint8Array | undefined;
}
export interface MedicalScribeConfigurationEvent {
  VocabularyName?: string | undefined;
  VocabularyFilterName?: string | undefined;
  VocabularyFilterMethod?: MedicalScribeVocabularyFilterMethod | undefined;
  ResourceAccessRoleArn: string | undefined;
  ChannelDefinitions?: MedicalScribeChannelDefinition[] | undefined;
  EncryptionSettings?: MedicalScribeEncryptionSettings | undefined;
  PostStreamAnalyticsSettings:
    | MedicalScribePostStreamAnalyticsSettings
    | undefined;
}
export declare const MedicalScribeSessionControlEventType: {
  readonly END_OF_SESSION: "END_OF_SESSION";
};
export type MedicalScribeSessionControlEventType =
  (typeof MedicalScribeSessionControlEventType)[keyof typeof MedicalScribeSessionControlEventType];
export interface MedicalScribeSessionControlEvent {
  Type: MedicalScribeSessionControlEventType | undefined;
}
export type MedicalScribeInputStream =
  | MedicalScribeInputStream.AudioEventMember
  | MedicalScribeInputStream.ConfigurationEventMember
  | MedicalScribeInputStream.SessionControlEventMember
  | MedicalScribeInputStream.$UnknownMember;
export declare namespace MedicalScribeInputStream {
  interface AudioEventMember {
    AudioEvent: MedicalScribeAudioEvent;
    SessionControlEvent?: never;
    ConfigurationEvent?: never;
    $unknown?: never;
  }
  interface SessionControlEventMember {
    AudioEvent?: never;
    SessionControlEvent: MedicalScribeSessionControlEvent;
    ConfigurationEvent?: never;
    $unknown?: never;
  }
  interface ConfigurationEventMember {
    AudioEvent?: never;
    SessionControlEvent?: never;
    ConfigurationEvent: MedicalScribeConfigurationEvent;
    $unknown?: never;
  }
  interface $UnknownMember {
    AudioEvent?: never;
    SessionControlEvent?: never;
    ConfigurationEvent?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    AudioEvent: (value: MedicalScribeAudioEvent) => T;
    SessionControlEvent: (value: MedicalScribeSessionControlEvent) => T;
    ConfigurationEvent: (value: MedicalScribeConfigurationEvent) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: MedicalScribeInputStream, visitor: Visitor<T>) => T;
}
export declare const MedicalScribeTranscriptItemType: {
  readonly PRONUNCIATION: "pronunciation";
  readonly PUNCTUATION: "punctuation";
};
export type MedicalScribeTranscriptItemType =
  (typeof MedicalScribeTranscriptItemType)[keyof typeof MedicalScribeTranscriptItemType];
export interface MedicalScribeTranscriptItem {
  BeginAudioTime?: number | undefined;
  EndAudioTime?: number | undefined;
  Type?: MedicalScribeTranscriptItemType | undefined;
  Confidence?: number | undefined;
  Content?: string | undefined;
  VocabularyFilterMatch?: boolean | undefined;
}
export interface MedicalScribeTranscriptSegment {
  SegmentId?: string | undefined;
  BeginAudioTime?: number | undefined;
  EndAudioTime?: number | undefined;
  Content?: string | undefined;
  Items?: MedicalScribeTranscriptItem[] | undefined;
  IsPartial?: boolean | undefined;
  ChannelId?: string | undefined;
}
export interface MedicalScribeTranscriptEvent {
  TranscriptSegment?: MedicalScribeTranscriptSegment | undefined;
}
export type MedicalScribeResultStream =
  | MedicalScribeResultStream.BadRequestExceptionMember
  | MedicalScribeResultStream.ConflictExceptionMember
  | MedicalScribeResultStream.InternalFailureExceptionMember
  | MedicalScribeResultStream.LimitExceededExceptionMember
  | MedicalScribeResultStream.ServiceUnavailableExceptionMember
  | MedicalScribeResultStream.TranscriptEventMember
  | MedicalScribeResultStream.$UnknownMember;
export declare namespace MedicalScribeResultStream {
  interface TranscriptEventMember {
    TranscriptEvent: MedicalScribeTranscriptEvent;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface BadRequestExceptionMember {
    TranscriptEvent?: never;
    BadRequestException: BadRequestException;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface LimitExceededExceptionMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException: LimitExceededException;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface InternalFailureExceptionMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException: InternalFailureException;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface ConflictExceptionMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException: ConflictException;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface ServiceUnavailableExceptionMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException: ServiceUnavailableException;
    $unknown?: never;
  }
  interface $UnknownMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    TranscriptEvent: (value: MedicalScribeTranscriptEvent) => T;
    BadRequestException: (value: BadRequestException) => T;
    LimitExceededException: (value: LimitExceededException) => T;
    InternalFailureException: (value: InternalFailureException) => T;
    ConflictException: (value: ConflictException) => T;
    ServiceUnavailableException: (value: ServiceUnavailableException) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: MedicalScribeResultStream, visitor: Visitor<T>) => T;
}
export interface MedicalTranscript {
  Results?: MedicalResult[] | undefined;
}
export interface MedicalTranscriptEvent {
  Transcript?: MedicalTranscript | undefined;
}
export type MedicalTranscriptResultStream =
  | MedicalTranscriptResultStream.BadRequestExceptionMember
  | MedicalTranscriptResultStream.ConflictExceptionMember
  | MedicalTranscriptResultStream.InternalFailureExceptionMember
  | MedicalTranscriptResultStream.LimitExceededExceptionMember
  | MedicalTranscriptResultStream.ServiceUnavailableExceptionMember
  | MedicalTranscriptResultStream.TranscriptEventMember
  | MedicalTranscriptResultStream.$UnknownMember;
export declare namespace MedicalTranscriptResultStream {
  interface TranscriptEventMember {
    TranscriptEvent: MedicalTranscriptEvent;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface BadRequestExceptionMember {
    TranscriptEvent?: never;
    BadRequestException: BadRequestException;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface LimitExceededExceptionMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException: LimitExceededException;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface InternalFailureExceptionMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException: InternalFailureException;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface ConflictExceptionMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException: ConflictException;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface ServiceUnavailableExceptionMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException: ServiceUnavailableException;
    $unknown?: never;
  }
  interface $UnknownMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    TranscriptEvent: (value: MedicalTranscriptEvent) => T;
    BadRequestException: (value: BadRequestException) => T;
    LimitExceededException: (value: LimitExceededException) => T;
    InternalFailureException: (value: InternalFailureException) => T;
    ConflictException: (value: ConflictException) => T;
    ServiceUnavailableException: (value: ServiceUnavailableException) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(
    value: MedicalTranscriptResultStream,
    visitor: Visitor<T>
  ) => T;
}
export declare const PartialResultsStability: {
  readonly HIGH: "high";
  readonly LOW: "low";
  readonly MEDIUM: "medium";
};
export type PartialResultsStability =
  (typeof PartialResultsStability)[keyof typeof PartialResultsStability];
export interface Result {
  ResultId?: string | undefined;
  StartTime?: number | undefined;
  EndTime?: number | undefined;
  IsPartial?: boolean | undefined;
  Alternatives?: Alternative[] | undefined;
  ChannelId?: string | undefined;
  LanguageCode?: LanguageCode | undefined;
  LanguageIdentification?: LanguageWithScore[] | undefined;
}
export declare const Specialty: {
  readonly CARDIOLOGY: "CARDIOLOGY";
  readonly NEUROLOGY: "NEUROLOGY";
  readonly ONCOLOGY: "ONCOLOGY";
  readonly PRIMARYCARE: "PRIMARYCARE";
  readonly RADIOLOGY: "RADIOLOGY";
  readonly UROLOGY: "UROLOGY";
};
export type Specialty = (typeof Specialty)[keyof typeof Specialty];
export declare const VocabularyFilterMethod: {
  readonly MASK: "mask";
  readonly REMOVE: "remove";
  readonly TAG: "tag";
};
export type VocabularyFilterMethod =
  (typeof VocabularyFilterMethod)[keyof typeof VocabularyFilterMethod];
export interface StartCallAnalyticsStreamTranscriptionRequest {
  LanguageCode: CallAnalyticsLanguageCode | undefined;
  MediaSampleRateHertz: number | undefined;
  MediaEncoding: MediaEncoding | undefined;
  VocabularyName?: string | undefined;
  SessionId?: string | undefined;
  AudioStream: AsyncIterable<AudioStream> | undefined;
  VocabularyFilterName?: string | undefined;
  VocabularyFilterMethod?: VocabularyFilterMethod | undefined;
  LanguageModelName?: string | undefined;
  EnablePartialResultsStabilization?: boolean | undefined;
  PartialResultsStability?: PartialResultsStability | undefined;
  ContentIdentificationType?: ContentIdentificationType | undefined;
  ContentRedactionType?: ContentRedactionType | undefined;
  PiiEntityTypes?: string | undefined;
}
export interface StartCallAnalyticsStreamTranscriptionResponse {
  RequestId?: string | undefined;
  LanguageCode?: CallAnalyticsLanguageCode | undefined;
  MediaSampleRateHertz?: number | undefined;
  MediaEncoding?: MediaEncoding | undefined;
  VocabularyName?: string | undefined;
  SessionId?: string | undefined;
  CallAnalyticsTranscriptResultStream?:
    | AsyncIterable<CallAnalyticsTranscriptResultStream>
    | undefined;
  VocabularyFilterName?: string | undefined;
  VocabularyFilterMethod?: VocabularyFilterMethod | undefined;
  LanguageModelName?: string | undefined;
  EnablePartialResultsStabilization?: boolean | undefined;
  PartialResultsStability?: PartialResultsStability | undefined;
  ContentIdentificationType?: ContentIdentificationType | undefined;
  ContentRedactionType?: ContentRedactionType | undefined;
  PiiEntityTypes?: string | undefined;
}
export interface StartMedicalScribeStreamRequest {
  SessionId?: string | undefined;
  LanguageCode: MedicalScribeLanguageCode | undefined;
  MediaSampleRateHertz: number | undefined;
  MediaEncoding: MedicalScribeMediaEncoding | undefined;
  InputStream: AsyncIterable<MedicalScribeInputStream> | undefined;
}
export interface StartMedicalScribeStreamResponse {
  SessionId?: string | undefined;
  RequestId?: string | undefined;
  LanguageCode?: MedicalScribeLanguageCode | undefined;
  MediaSampleRateHertz?: number | undefined;
  MediaEncoding?: MedicalScribeMediaEncoding | undefined;
  ResultStream?: AsyncIterable<MedicalScribeResultStream> | undefined;
}
export declare const Type: {
  readonly CONVERSATION: "CONVERSATION";
  readonly DICTATION: "DICTATION";
};
export type Type = (typeof Type)[keyof typeof Type];
export interface StartMedicalStreamTranscriptionRequest {
  LanguageCode: LanguageCode | undefined;
  MediaSampleRateHertz: number | undefined;
  MediaEncoding: MediaEncoding | undefined;
  VocabularyName?: string | undefined;
  Specialty: Specialty | undefined;
  Type: Type | undefined;
  ShowSpeakerLabel?: boolean | undefined;
  SessionId?: string | undefined;
  AudioStream: AsyncIterable<AudioStream> | undefined;
  EnableChannelIdentification?: boolean | undefined;
  NumberOfChannels?: number | undefined;
  ContentIdentificationType?: MedicalContentIdentificationType | undefined;
}
export interface StartMedicalStreamTranscriptionResponse {
  RequestId?: string | undefined;
  LanguageCode?: LanguageCode | undefined;
  MediaSampleRateHertz?: number | undefined;
  MediaEncoding?: MediaEncoding | undefined;
  VocabularyName?: string | undefined;
  Specialty?: Specialty | undefined;
  Type?: Type | undefined;
  ShowSpeakerLabel?: boolean | undefined;
  SessionId?: string | undefined;
  TranscriptResultStream?:
    | AsyncIterable<MedicalTranscriptResultStream>
    | undefined;
  EnableChannelIdentification?: boolean | undefined;
  NumberOfChannels?: number | undefined;
  ContentIdentificationType?: MedicalContentIdentificationType | undefined;
}
export interface StartStreamTranscriptionRequest {
  LanguageCode?: LanguageCode | undefined;
  MediaSampleRateHertz: number | undefined;
  MediaEncoding: MediaEncoding | undefined;
  VocabularyName?: string | undefined;
  SessionId?: string | undefined;
  AudioStream: AsyncIterable<AudioStream> | undefined;
  VocabularyFilterName?: string | undefined;
  VocabularyFilterMethod?: VocabularyFilterMethod | undefined;
  ShowSpeakerLabel?: boolean | undefined;
  EnableChannelIdentification?: boolean | undefined;
  NumberOfChannels?: number | undefined;
  EnablePartialResultsStabilization?: boolean | undefined;
  PartialResultsStability?: PartialResultsStability | undefined;
  ContentIdentificationType?: ContentIdentificationType | undefined;
  ContentRedactionType?: ContentRedactionType | undefined;
  PiiEntityTypes?: string | undefined;
  LanguageModelName?: string | undefined;
  IdentifyLanguage?: boolean | undefined;
  LanguageOptions?: string | undefined;
  PreferredLanguage?: LanguageCode | undefined;
  IdentifyMultipleLanguages?: boolean | undefined;
  VocabularyNames?: string | undefined;
  VocabularyFilterNames?: string | undefined;
}
export interface Transcript {
  Results?: Result[] | undefined;
}
export interface TranscriptEvent {
  Transcript?: Transcript | undefined;
}
export type TranscriptResultStream =
  | TranscriptResultStream.BadRequestExceptionMember
  | TranscriptResultStream.ConflictExceptionMember
  | TranscriptResultStream.InternalFailureExceptionMember
  | TranscriptResultStream.LimitExceededExceptionMember
  | TranscriptResultStream.ServiceUnavailableExceptionMember
  | TranscriptResultStream.TranscriptEventMember
  | TranscriptResultStream.$UnknownMember;
export declare namespace TranscriptResultStream {
  interface TranscriptEventMember {
    TranscriptEvent: TranscriptEvent;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface BadRequestExceptionMember {
    TranscriptEvent?: never;
    BadRequestException: BadRequestException;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface LimitExceededExceptionMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException: LimitExceededException;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface InternalFailureExceptionMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException: InternalFailureException;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface ConflictExceptionMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException: ConflictException;
    ServiceUnavailableException?: never;
    $unknown?: never;
  }
  interface ServiceUnavailableExceptionMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException: ServiceUnavailableException;
    $unknown?: never;
  }
  interface $UnknownMember {
    TranscriptEvent?: never;
    BadRequestException?: never;
    LimitExceededException?: never;
    InternalFailureException?: never;
    ConflictException?: never;
    ServiceUnavailableException?: never;
    $unknown: [string, any];
  }
  interface Visitor<T> {
    TranscriptEvent: (value: TranscriptEvent) => T;
    BadRequestException: (value: BadRequestException) => T;
    LimitExceededException: (value: LimitExceededException) => T;
    InternalFailureException: (value: InternalFailureException) => T;
    ConflictException: (value: ConflictException) => T;
    ServiceUnavailableException: (value: ServiceUnavailableException) => T;
    _: (name: string, value: any) => T;
  }
  const visit: <T>(value: TranscriptResultStream, visitor: Visitor<T>) => T;
}
export interface StartStreamTranscriptionResponse {
  RequestId?: string | undefined;
  LanguageCode?: LanguageCode | undefined;
  MediaSampleRateHertz?: number | undefined;
  MediaEncoding?: MediaEncoding | undefined;
  VocabularyName?: string | undefined;
  SessionId?: string | undefined;
  TranscriptResultStream?: AsyncIterable<TranscriptResultStream> | undefined;
  VocabularyFilterName?: string | undefined;
  VocabularyFilterMethod?: VocabularyFilterMethod | undefined;
  ShowSpeakerLabel?: boolean | undefined;
  EnableChannelIdentification?: boolean | undefined;
  NumberOfChannels?: number | undefined;
  EnablePartialResultsStabilization?: boolean | undefined;
  PartialResultsStability?: PartialResultsStability | undefined;
  ContentIdentificationType?: ContentIdentificationType | undefined;
  ContentRedactionType?: ContentRedactionType | undefined;
  PiiEntityTypes?: string | undefined;
  LanguageModelName?: string | undefined;
  IdentifyLanguage?: boolean | undefined;
  LanguageOptions?: string | undefined;
  PreferredLanguage?: LanguageCode | undefined;
  IdentifyMultipleLanguages?: boolean | undefined;
  VocabularyNames?: string | undefined;
  VocabularyFilterNames?: string | undefined;
}
export declare const AudioStreamFilterSensitiveLog: (obj: AudioStream) => any;
export declare const CallAnalyticsTranscriptResultStreamFilterSensitiveLog: (
  obj: CallAnalyticsTranscriptResultStream
) => any;
export declare const MedicalScribeInputStreamFilterSensitiveLog: (
  obj: MedicalScribeInputStream
) => any;
export declare const MedicalScribeResultStreamFilterSensitiveLog: (
  obj: MedicalScribeResultStream
) => any;
export declare const MedicalTranscriptResultStreamFilterSensitiveLog: (
  obj: MedicalTranscriptResultStream
) => any;
export declare const StartCallAnalyticsStreamTranscriptionRequestFilterSensitiveLog: (
  obj: StartCallAnalyticsStreamTranscriptionRequest
) => any;
export declare const StartCallAnalyticsStreamTranscriptionResponseFilterSensitiveLog: (
  obj: StartCallAnalyticsStreamTranscriptionResponse
) => any;
export declare const StartMedicalScribeStreamRequestFilterSensitiveLog: (
  obj: StartMedicalScribeStreamRequest
) => any;
export declare const StartMedicalScribeStreamResponseFilterSensitiveLog: (
  obj: StartMedicalScribeStreamResponse
) => any;
export declare const StartMedicalStreamTranscriptionRequestFilterSensitiveLog: (
  obj: StartMedicalStreamTranscriptionRequest
) => any;
export declare const StartMedicalStreamTranscriptionResponseFilterSensitiveLog: (
  obj: StartMedicalStreamTranscriptionResponse
) => any;
export declare const StartStreamTranscriptionRequestFilterSensitiveLog: (
  obj: StartStreamTranscriptionRequest
) => any;
export declare const TranscriptResultStreamFilterSensitiveLog: (
  obj: TranscriptResultStream
) => any;
export declare const StartStreamTranscriptionResponseFilterSensitiveLog: (
  obj: StartStreamTranscriptionResponse
) => any;
