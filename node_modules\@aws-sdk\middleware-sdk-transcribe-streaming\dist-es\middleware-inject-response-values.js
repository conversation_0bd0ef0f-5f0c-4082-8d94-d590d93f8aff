import { v4 } from "uuid";
export const injectResponseValuesMiddleware = (config) => (next) => async (args) => {
    if (args.input.SessionId === undefined && isWebSocket(config)) {
        args.input.SessionId = v4();
    }
    const requestParams = {
        ...args.input,
    };
    const response = await next(args);
    const output = response.output;
    for (const key of Object.keys(output)) {
        if (output[key] === undefined && requestParams[key]) {
            output[key] = requestParams[key];
        }
    }
    return response;
};
const isWebSocket = (config) => config.requestHandler.metadata?.handlerProtocol?.includes("websocket");
export const injectResponseValuesMiddlewareOptions = {
    step: "initialize",
    name: "injectResponseValuesMiddleware",
    tags: ["WEBSOCKET", "EVENT_STREAM"],
    override: true,
};
