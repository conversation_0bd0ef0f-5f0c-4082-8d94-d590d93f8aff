"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  eventStreamPayloadHandler: () => eventStreamPayloadHandler,
  getTranscribeStreamingPlugin: () => getTranscribeStreamingPlugin,
  injectResponseValuesMiddleware: () => injectResponseValuesMiddleware,
  injectResponseValuesMiddlewareOptions: () => injectResponseValuesMiddlewareOptions,
  websocketPortMiddleware: () => websocketPortMiddleware,
  websocketPortMiddlewareOptions: () => websocketPortMiddlewareOptions
});
module.exports = __toCommonJS(index_exports);

// src/eventstream-handler.ts
var eventStreamPayloadHandler = {
  handle: /* @__PURE__ */ __name((next, args) => next(args), "handle")
};

// src/middleware-inject-response-values.ts
var import_uuid = require("uuid");
var injectResponseValuesMiddleware = /* @__PURE__ */ __name((config) => (next) => async (args) => {
  if (args.input.SessionId === void 0 && isWebSocket(config)) {
    args.input.SessionId = (0, import_uuid.v4)();
  }
  const requestParams = {
    ...args.input
  };
  const response = await next(args);
  const output = response.output;
  for (const key of Object.keys(output)) {
    if (output[key] === void 0 && requestParams[key]) {
      output[key] = requestParams[key];
    }
  }
  return response;
}, "injectResponseValuesMiddleware");
var isWebSocket = /* @__PURE__ */ __name((config) => config.requestHandler.metadata?.handlerProtocol?.includes("websocket"), "isWebSocket");
var injectResponseValuesMiddlewareOptions = {
  step: "initialize",
  name: "injectResponseValuesMiddleware",
  tags: ["WEBSOCKET", "EVENT_STREAM"],
  override: true
};

// src/middleware-port.ts
var import_protocol_http = require("@smithy/protocol-http");
var websocketPortMiddleware = /* @__PURE__ */ __name((options) => (next) => (args) => {
  const { request } = args;
  if (import_protocol_http.HttpRequest.isInstance(request) && options.requestHandler.metadata?.handlerProtocol?.includes("websocket")) {
    request.hostname = `${request.hostname}:8443`;
    request.headers.host = request.hostname;
  }
  return next(args);
}, "websocketPortMiddleware");
var websocketPortMiddlewareOptions = {
  name: "websocketPortMiddleware",
  tags: ["WEBSOCKET", "EVENT_STREAM", "PORT"],
  relation: "after",
  toMiddleware: "eventStreamHeaderMiddleware",
  override: true
};

// src/getTranscribeStreamingPlugin.ts
var getTranscribeStreamingPlugin = /* @__PURE__ */ __name((config) => ({
  applyToStack: /* @__PURE__ */ __name((clientStack) => {
    clientStack.addRelativeTo(websocketPortMiddleware(config), websocketPortMiddlewareOptions);
    clientStack.add(injectResponseValuesMiddleware(config), injectResponseValuesMiddlewareOptions);
  }, "applyToStack")
}), "getTranscribeStreamingPlugin");
// Annotate the CommonJS export names for ESM import in node:

0 && (module.exports = {
  eventStreamPayloadHandler,
  getTranscribeStreamingPlugin,
  injectResponseValuesMiddleware,
  injectResponseValuesMiddlewareOptions,
  websocketPortMiddleware,
  websocketPortMiddlewareOptions
});

