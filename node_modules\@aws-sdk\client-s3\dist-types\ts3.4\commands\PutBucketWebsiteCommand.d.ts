import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import { PutBucketWebsiteRequest } from "../models/models_1";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface PutBucketWebsiteCommandInput extends PutBucketWebsiteRequest {}
export interface PutBucketWebsiteCommandOutput extends __MetadataBearer {}
declare const PutBucketWebsiteCommand_base: {
  new (
    input: PutBucketWebsiteCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketWebsiteCommandInput,
    PutBucketWebsiteCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutBucketWebsiteCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutBucketWebsiteCommandInput,
    PutBucketWebsiteCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutBucketWebsiteCommand extends PutBucketWebsiteCommand_base {
  protected static __types: {
    api: {
      input: PutBucketWebsiteRequest;
      output: {};
    };
    sdk: {
      input: PutBucketWebsiteCommandInput;
      output: PutBucketWebsiteCommandOutput;
    };
  };
}
