const {
  TranscribeStreamingClient,
  StartStreamTranscriptionCommand,
} = require("@aws-sdk/client-transcribe-streaming");
const { createReadStream } = require("fs");
const { join } = require("path");

const LanguageCode = "en-US";
const MediaEncoding = "pcm";
const MediaSampleRateHertz = 16000; // Should be a number, not string

// Use environment variables or AWS credential chain instead of hardcoded credentials
// For temporary credentials, you'll need accessKeyId, secretAccessKey, AND sessionToken
const credentials = {
  accessKeyId: process.env.AWS_ACCESS_KEY_ID || "********************",
  secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || "hkSxaZ4RCWxVpgIbn+Xmo5LiaAxlfv7UVbSSYQyW",
  sessionToken: process.env.AWS_SESSION_TOKEN || "IQoJb3JpZ2luX2VjEKz//////////wEaCXVzLXdlc3QtMSJGMEQCIAlxSGKOrpUip/bZMHThWgFBHOGb0jByBv5vw28yFR0hAiAC5oI0rUhNKHfR6pBhrPXqZMpZrVN3skFQicci7cHY6Sr5Agi1//////////8BEAMaDDE3NTM3NjkzNzg2NyIMEz3183hI4qeSB/CYKs0CE1YKqF1Jrgqkr/lHrjNoiy6dZIBapSJYoYhFQhKSr4OP872dso9+qDIRI1/++ZoucpWyEx6ZjM9esHGq7IqcwKJi3Cup5rZXPQSPFJxT3IEzAdhIgilO4pwZTk174zDdkuucRGew0Bmwj+KTLZSE04rmLGD4I57nuuz99bS4oPauFMc7lP10g6X2mpiq7MfDX7Oyz1AWJOBSiXWH245pW+Ju8uyNkqXpTeGKZfFpMe8h8re9Hd6prGH8cwRqvaqx3Sx5iHClJIgN99+kwtAPnZi/t5Pf9vYnOeSC8ivzcmnkZGsGSiY3g6tF4QU+KD/2grF/LGMknKGAggXeafTnZeNRPycEE6/BYzIKMKZTY1Lix3zx5Sa9x2GozDXTYOY2X/xD61dyekCkscZyYK7nkHlSZkyoKiV9FLQ08r32fq5hN2dblVKCWZIgkZ7bMNnnvMMGOqgBk+tLmv6XDuFDshiR+b9gAKFzITNFCBQgHjI95V4MdPPKmaMmN91lpPpEyaFKDfw1PHrvSFMLSHVitjCQ4oxn1/7ySYNanFCCww3ZCM09z9Scr2wUeuzCSqb/09x8wxL4GOuW+tBjNxnXfT74y8P5SBAJ18g0ObiqQzp40CpAh/S/L5fV62Hirtpj6W4zspK2TA4bEXy702jSMQ593Rh3uHr4TW/9I0Je"
};

async function startRequest() {
  console.log("Starting AWS Transcribe Streaming...");

  try {
    const client = new TranscribeStreamingClient({
      region: "us-west-1",
      credentials,
      requestHandler: {
        // Add connection timeout and retry configuration
        connectionTimeout: 30000,
        socketTimeout: 30000,
      },
      maxAttempts: 3, // Retry failed requests
    });

    // Create audio stream with smaller chunks and delay to prevent overwhelming the service
    const audioStream = async function* () {
      const audio = createReadStream(join(__dirname, "test1.wav"), { // Replace with your audio file path
        highWaterMark: 1024 * 4 // Smaller chunks
      });

      for await (const chunk of audio) {
        yield { AudioEvent: { AudioChunk: chunk } };
        // Small delay to prevent overwhelming the stream
        await new Promise(resolve => setTimeout(resolve, 10));
      }
    };

    const params = {
      LanguageCode,
      MediaEncoding,
      MediaSampleRateHertz,
      AudioStream: audioStream(),
    };

    console.log("Sending transcription request...");
    const command = new StartStreamTranscriptionCommand(params);
    const response = await client.send(command);

    console.log("Receiving transcription results...");
    for await (const event of response.TranscriptResultStream) {
      if (event.TranscriptEvent) {
        const results = event.TranscriptEvent.Transcript.Results;
        if (results && results.length > 0) {
          const transcript = results[0];
          if (transcript.Alternatives && transcript.Alternatives.length > 0) {
            const text = transcript.Alternatives[0].Transcript;
            console.log(`Transcript: ${text}`);
            console.log(`Is Final: ${transcript.IsPartial ? 'No' : 'Yes'}`);
          }
        }
      }
    }

    console.log("Transcription completed successfully!");

  } catch (err) {
    console.error("Error during transcription:");
    console.error("Error name:", err.name);
    console.error("Error message:", err.message);

    if (err.name === 'CredentialsProviderError') {
      console.error("\n🔑 Credential Issue:");
      console.error("- Check if your AWS credentials are valid and not expired");
      console.error("- For temporary credentials, ensure you have sessionToken");
      console.error("- Consider using AWS CLI: aws configure");
    } else if (err.message.includes('HTTP/2')) {
      console.error("\n🌐 Network Issue:");
      console.error("- Try switching to a different AWS region");
      console.error("- Check your internet connection");
      console.error("- The service might be temporarily unavailable");
    }

    console.error("\nFull error details:", err);
  }
}

startRequest();