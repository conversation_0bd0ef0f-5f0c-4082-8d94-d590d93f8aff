import { injectResponseValuesMiddleware, injectResponseValuesMiddlewareOptions, } from "./middleware-inject-response-values";
import { websocketPortMiddleware, websocketPortMiddlewareOptions } from "./middleware-port";
export const getTranscribeStreamingPlugin = (config) => ({
    applyToStack: (clientStack) => {
        clientStack.addRelativeTo(websocketPortMiddleware(config), websocketPortMiddlewareOptions);
        clientStack.add(injectResponseValuesMiddleware(config), injectResponseValuesMiddlewareOptions);
    },
});
