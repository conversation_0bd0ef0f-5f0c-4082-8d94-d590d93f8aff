import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  PutObjectLockConfigurationOutput,
  PutObjectLockConfigurationRequest,
} from "../models/models_1";
import {
  S3ClientResolvedConfig,
  ServiceInputTypes,
  ServiceOutputTypes,
} from "../S3Client";
export { __MetadataBearer };
export { $Command };
export interface PutObjectLockConfigurationCommandInput
  extends PutObjectLockConfigurationRequest {}
export interface PutObjectLockConfigurationCommandOutput
  extends PutObjectLockConfigurationOutput,
    __MetadataBearer {}
declare const PutObjectLockConfigurationCommand_base: {
  new (
    input: PutObjectLockConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutObjectLockConfigurationCommandInput,
    PutObjectLockConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: PutObjectLockConfigurationCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    PutObjectLockConfigurationCommandInput,
    PutObjectLockConfigurationCommandOutput,
    S3ClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class PutObjectLockConfigurationCommand extends PutObjectLockConfigurationCommand_base {
  protected static __types: {
    api: {
      input: PutObjectLockConfigurationRequest;
      output: PutObjectLockConfigurationOutput;
    };
    sdk: {
      input: PutObjectLockConfigurationCommandInput;
      output: PutObjectLockConfigurationCommandOutput;
    };
  };
}
