import {
  HttpRequest as __HttpRequest,
  HttpResponse as __HttpResponse,
} from "@smithy/protocol-http";
import {
  EventStreamSerdeContext as __EventStreamSerdeContext,
  SdkStreamSerdeContext as __SdkStreamSerdeContext,
  SerdeContext as __SerdeContext,
} from "@smithy/types";
import {
  AbortMultipartUploadCommandInput,
  AbortMultipartUploadCommandOutput,
} from "../commands/AbortMultipartUploadCommand";
import {
  CompleteMultipartUploadCommandInput,
  CompleteMultipartUploadCommandOutput,
} from "../commands/CompleteMultipartUploadCommand";
import {
  CopyObjectCommandInput,
  CopyObjectCommandOutput,
} from "../commands/CopyObjectCommand";
import {
  CreateBucketCommandInput,
  CreateBucketCommandOutput,
} from "../commands/CreateBucketCommand";
import {
  CreateBucketMetadataTableConfigurationCommandInput,
  CreateBucketMetadataTableConfigurationCommandOutput,
} from "../commands/CreateBucketMetadataTableConfigurationCommand";
import {
  CreateMultipartUploadCommandInput,
  CreateMultipartUploadCommandOutput,
} from "../commands/CreateMultipartUploadCommand";
import {
  CreateSessionCommandInput,
  CreateSessionCommandOutput,
} from "../commands/CreateSessionCommand";
import {
  DeleteBucketAnalyticsConfigurationCommandInput,
  DeleteBucketAnalyticsConfigurationCommandOutput,
} from "../commands/DeleteBucketAnalyticsConfigurationCommand";
import {
  DeleteBucketCommandInput,
  DeleteBucketCommandOutput,
} from "../commands/DeleteBucketCommand";
import {
  DeleteBucketCorsCommandInput,
  DeleteBucketCorsCommandOutput,
} from "../commands/DeleteBucketCorsCommand";
import {
  DeleteBucketEncryptionCommandInput,
  DeleteBucketEncryptionCommandOutput,
} from "../commands/DeleteBucketEncryptionCommand";
import {
  DeleteBucketIntelligentTieringConfigurationCommandInput,
  DeleteBucketIntelligentTieringConfigurationCommandOutput,
} from "../commands/DeleteBucketIntelligentTieringConfigurationCommand";
import {
  DeleteBucketInventoryConfigurationCommandInput,
  DeleteBucketInventoryConfigurationCommandOutput,
} from "../commands/DeleteBucketInventoryConfigurationCommand";
import {
  DeleteBucketLifecycleCommandInput,
  DeleteBucketLifecycleCommandOutput,
} from "../commands/DeleteBucketLifecycleCommand";
import {
  DeleteBucketMetadataTableConfigurationCommandInput,
  DeleteBucketMetadataTableConfigurationCommandOutput,
} from "../commands/DeleteBucketMetadataTableConfigurationCommand";
import {
  DeleteBucketMetricsConfigurationCommandInput,
  DeleteBucketMetricsConfigurationCommandOutput,
} from "../commands/DeleteBucketMetricsConfigurationCommand";
import {
  DeleteBucketOwnershipControlsCommandInput,
  DeleteBucketOwnershipControlsCommandOutput,
} from "../commands/DeleteBucketOwnershipControlsCommand";
import {
  DeleteBucketPolicyCommandInput,
  DeleteBucketPolicyCommandOutput,
} from "../commands/DeleteBucketPolicyCommand";
import {
  DeleteBucketReplicationCommandInput,
  DeleteBucketReplicationCommandOutput,
} from "../commands/DeleteBucketReplicationCommand";
import {
  DeleteBucketTaggingCommandInput,
  DeleteBucketTaggingCommandOutput,
} from "../commands/DeleteBucketTaggingCommand";
import {
  DeleteBucketWebsiteCommandInput,
  DeleteBucketWebsiteCommandOutput,
} from "../commands/DeleteBucketWebsiteCommand";
import {
  DeleteObjectCommandInput,
  DeleteObjectCommandOutput,
} from "../commands/DeleteObjectCommand";
import {
  DeleteObjectsCommandInput,
  DeleteObjectsCommandOutput,
} from "../commands/DeleteObjectsCommand";
import {
  DeleteObjectTaggingCommandInput,
  DeleteObjectTaggingCommandOutput,
} from "../commands/DeleteObjectTaggingCommand";
import {
  DeletePublicAccessBlockCommandInput,
  DeletePublicAccessBlockCommandOutput,
} from "../commands/DeletePublicAccessBlockCommand";
import {
  GetBucketAccelerateConfigurationCommandInput,
  GetBucketAccelerateConfigurationCommandOutput,
} from "../commands/GetBucketAccelerateConfigurationCommand";
import {
  GetBucketAclCommandInput,
  GetBucketAclCommandOutput,
} from "../commands/GetBucketAclCommand";
import {
  GetBucketAnalyticsConfigurationCommandInput,
  GetBucketAnalyticsConfigurationCommandOutput,
} from "../commands/GetBucketAnalyticsConfigurationCommand";
import {
  GetBucketCorsCommandInput,
  GetBucketCorsCommandOutput,
} from "../commands/GetBucketCorsCommand";
import {
  GetBucketEncryptionCommandInput,
  GetBucketEncryptionCommandOutput,
} from "../commands/GetBucketEncryptionCommand";
import {
  GetBucketIntelligentTieringConfigurationCommandInput,
  GetBucketIntelligentTieringConfigurationCommandOutput,
} from "../commands/GetBucketIntelligentTieringConfigurationCommand";
import {
  GetBucketInventoryConfigurationCommandInput,
  GetBucketInventoryConfigurationCommandOutput,
} from "../commands/GetBucketInventoryConfigurationCommand";
import {
  GetBucketLifecycleConfigurationCommandInput,
  GetBucketLifecycleConfigurationCommandOutput,
} from "../commands/GetBucketLifecycleConfigurationCommand";
import {
  GetBucketLocationCommandInput,
  GetBucketLocationCommandOutput,
} from "../commands/GetBucketLocationCommand";
import {
  GetBucketLoggingCommandInput,
  GetBucketLoggingCommandOutput,
} from "../commands/GetBucketLoggingCommand";
import {
  GetBucketMetadataTableConfigurationCommandInput,
  GetBucketMetadataTableConfigurationCommandOutput,
} from "../commands/GetBucketMetadataTableConfigurationCommand";
import {
  GetBucketMetricsConfigurationCommandInput,
  GetBucketMetricsConfigurationCommandOutput,
} from "../commands/GetBucketMetricsConfigurationCommand";
import {
  GetBucketNotificationConfigurationCommandInput,
  GetBucketNotificationConfigurationCommandOutput,
} from "../commands/GetBucketNotificationConfigurationCommand";
import {
  GetBucketOwnershipControlsCommandInput,
  GetBucketOwnershipControlsCommandOutput,
} from "../commands/GetBucketOwnershipControlsCommand";
import {
  GetBucketPolicyCommandInput,
  GetBucketPolicyCommandOutput,
} from "../commands/GetBucketPolicyCommand";
import {
  GetBucketPolicyStatusCommandInput,
  GetBucketPolicyStatusCommandOutput,
} from "../commands/GetBucketPolicyStatusCommand";
import {
  GetBucketReplicationCommandInput,
  GetBucketReplicationCommandOutput,
} from "../commands/GetBucketReplicationCommand";
import {
  GetBucketRequestPaymentCommandInput,
  GetBucketRequestPaymentCommandOutput,
} from "../commands/GetBucketRequestPaymentCommand";
import {
  GetBucketTaggingCommandInput,
  GetBucketTaggingCommandOutput,
} from "../commands/GetBucketTaggingCommand";
import {
  GetBucketVersioningCommandInput,
  GetBucketVersioningCommandOutput,
} from "../commands/GetBucketVersioningCommand";
import {
  GetBucketWebsiteCommandInput,
  GetBucketWebsiteCommandOutput,
} from "../commands/GetBucketWebsiteCommand";
import {
  GetObjectAclCommandInput,
  GetObjectAclCommandOutput,
} from "../commands/GetObjectAclCommand";
import {
  GetObjectAttributesCommandInput,
  GetObjectAttributesCommandOutput,
} from "../commands/GetObjectAttributesCommand";
import {
  GetObjectCommandInput,
  GetObjectCommandOutput,
} from "../commands/GetObjectCommand";
import {
  GetObjectLegalHoldCommandInput,
  GetObjectLegalHoldCommandOutput,
} from "../commands/GetObjectLegalHoldCommand";
import {
  GetObjectLockConfigurationCommandInput,
  GetObjectLockConfigurationCommandOutput,
} from "../commands/GetObjectLockConfigurationCommand";
import {
  GetObjectRetentionCommandInput,
  GetObjectRetentionCommandOutput,
} from "../commands/GetObjectRetentionCommand";
import {
  GetObjectTaggingCommandInput,
  GetObjectTaggingCommandOutput,
} from "../commands/GetObjectTaggingCommand";
import {
  GetObjectTorrentCommandInput,
  GetObjectTorrentCommandOutput,
} from "../commands/GetObjectTorrentCommand";
import {
  GetPublicAccessBlockCommandInput,
  GetPublicAccessBlockCommandOutput,
} from "../commands/GetPublicAccessBlockCommand";
import {
  HeadBucketCommandInput,
  HeadBucketCommandOutput,
} from "../commands/HeadBucketCommand";
import {
  HeadObjectCommandInput,
  HeadObjectCommandOutput,
} from "../commands/HeadObjectCommand";
import {
  ListBucketAnalyticsConfigurationsCommandInput,
  ListBucketAnalyticsConfigurationsCommandOutput,
} from "../commands/ListBucketAnalyticsConfigurationsCommand";
import {
  ListBucketIntelligentTieringConfigurationsCommandInput,
  ListBucketIntelligentTieringConfigurationsCommandOutput,
} from "../commands/ListBucketIntelligentTieringConfigurationsCommand";
import {
  ListBucketInventoryConfigurationsCommandInput,
  ListBucketInventoryConfigurationsCommandOutput,
} from "../commands/ListBucketInventoryConfigurationsCommand";
import {
  ListBucketMetricsConfigurationsCommandInput,
  ListBucketMetricsConfigurationsCommandOutput,
} from "../commands/ListBucketMetricsConfigurationsCommand";
import {
  ListBucketsCommandInput,
  ListBucketsCommandOutput,
} from "../commands/ListBucketsCommand";
import {
  ListDirectoryBucketsCommandInput,
  ListDirectoryBucketsCommandOutput,
} from "../commands/ListDirectoryBucketsCommand";
import {
  ListMultipartUploadsCommandInput,
  ListMultipartUploadsCommandOutput,
} from "../commands/ListMultipartUploadsCommand";
import {
  ListObjectsCommandInput,
  ListObjectsCommandOutput,
} from "../commands/ListObjectsCommand";
import {
  ListObjectsV2CommandInput,
  ListObjectsV2CommandOutput,
} from "../commands/ListObjectsV2Command";
import {
  ListObjectVersionsCommandInput,
  ListObjectVersionsCommandOutput,
} from "../commands/ListObjectVersionsCommand";
import {
  ListPartsCommandInput,
  ListPartsCommandOutput,
} from "../commands/ListPartsCommand";
import {
  PutBucketAccelerateConfigurationCommandInput,
  PutBucketAccelerateConfigurationCommandOutput,
} from "../commands/PutBucketAccelerateConfigurationCommand";
import {
  PutBucketAclCommandInput,
  PutBucketAclCommandOutput,
} from "../commands/PutBucketAclCommand";
import {
  PutBucketAnalyticsConfigurationCommandInput,
  PutBucketAnalyticsConfigurationCommandOutput,
} from "../commands/PutBucketAnalyticsConfigurationCommand";
import {
  PutBucketCorsCommandInput,
  PutBucketCorsCommandOutput,
} from "../commands/PutBucketCorsCommand";
import {
  PutBucketEncryptionCommandInput,
  PutBucketEncryptionCommandOutput,
} from "../commands/PutBucketEncryptionCommand";
import {
  PutBucketIntelligentTieringConfigurationCommandInput,
  PutBucketIntelligentTieringConfigurationCommandOutput,
} from "../commands/PutBucketIntelligentTieringConfigurationCommand";
import {
  PutBucketInventoryConfigurationCommandInput,
  PutBucketInventoryConfigurationCommandOutput,
} from "../commands/PutBucketInventoryConfigurationCommand";
import {
  PutBucketLifecycleConfigurationCommandInput,
  PutBucketLifecycleConfigurationCommandOutput,
} from "../commands/PutBucketLifecycleConfigurationCommand";
import {
  PutBucketLoggingCommandInput,
  PutBucketLoggingCommandOutput,
} from "../commands/PutBucketLoggingCommand";
import {
  PutBucketMetricsConfigurationCommandInput,
  PutBucketMetricsConfigurationCommandOutput,
} from "../commands/PutBucketMetricsConfigurationCommand";
import {
  PutBucketNotificationConfigurationCommandInput,
  PutBucketNotificationConfigurationCommandOutput,
} from "../commands/PutBucketNotificationConfigurationCommand";
import {
  PutBucketOwnershipControlsCommandInput,
  PutBucketOwnershipControlsCommandOutput,
} from "../commands/PutBucketOwnershipControlsCommand";
import {
  PutBucketPolicyCommandInput,
  PutBucketPolicyCommandOutput,
} from "../commands/PutBucketPolicyCommand";
import {
  PutBucketReplicationCommandInput,
  PutBucketReplicationCommandOutput,
} from "../commands/PutBucketReplicationCommand";
import {
  PutBucketRequestPaymentCommandInput,
  PutBucketRequestPaymentCommandOutput,
} from "../commands/PutBucketRequestPaymentCommand";
import {
  PutBucketTaggingCommandInput,
  PutBucketTaggingCommandOutput,
} from "../commands/PutBucketTaggingCommand";
import {
  PutBucketVersioningCommandInput,
  PutBucketVersioningCommandOutput,
} from "../commands/PutBucketVersioningCommand";
import {
  PutBucketWebsiteCommandInput,
  PutBucketWebsiteCommandOutput,
} from "../commands/PutBucketWebsiteCommand";
import {
  PutObjectAclCommandInput,
  PutObjectAclCommandOutput,
} from "../commands/PutObjectAclCommand";
import {
  PutObjectCommandInput,
  PutObjectCommandOutput,
} from "../commands/PutObjectCommand";
import {
  PutObjectLegalHoldCommandInput,
  PutObjectLegalHoldCommandOutput,
} from "../commands/PutObjectLegalHoldCommand";
import {
  PutObjectLockConfigurationCommandInput,
  PutObjectLockConfigurationCommandOutput,
} from "../commands/PutObjectLockConfigurationCommand";
import {
  PutObjectRetentionCommandInput,
  PutObjectRetentionCommandOutput,
} from "../commands/PutObjectRetentionCommand";
import {
  PutObjectTaggingCommandInput,
  PutObjectTaggingCommandOutput,
} from "../commands/PutObjectTaggingCommand";
import {
  PutPublicAccessBlockCommandInput,
  PutPublicAccessBlockCommandOutput,
} from "../commands/PutPublicAccessBlockCommand";
import {
  RenameObjectCommandInput,
  RenameObjectCommandOutput,
} from "../commands/RenameObjectCommand";
import {
  RestoreObjectCommandInput,
  RestoreObjectCommandOutput,
} from "../commands/RestoreObjectCommand";
import {
  SelectObjectContentCommandInput,
  SelectObjectContentCommandOutput,
} from "../commands/SelectObjectContentCommand";
import {
  UploadPartCommandInput,
  UploadPartCommandOutput,
} from "../commands/UploadPartCommand";
import {
  UploadPartCopyCommandInput,
  UploadPartCopyCommandOutput,
} from "../commands/UploadPartCopyCommand";
import {
  WriteGetObjectResponseCommandInput,
  WriteGetObjectResponseCommandOutput,
} from "../commands/WriteGetObjectResponseCommand";
export declare const se_AbortMultipartUploadCommand: (
  input: AbortMultipartUploadCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CompleteMultipartUploadCommand: (
  input: CompleteMultipartUploadCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CopyObjectCommand: (
  input: CopyObjectCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateBucketCommand: (
  input: CreateBucketCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateBucketMetadataTableConfigurationCommand: (
  input: CreateBucketMetadataTableConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateMultipartUploadCommand: (
  input: CreateMultipartUploadCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_CreateSessionCommand: (
  input: CreateSessionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBucketCommand: (
  input: DeleteBucketCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBucketAnalyticsConfigurationCommand: (
  input: DeleteBucketAnalyticsConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBucketCorsCommand: (
  input: DeleteBucketCorsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBucketEncryptionCommand: (
  input: DeleteBucketEncryptionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBucketIntelligentTieringConfigurationCommand: (
  input: DeleteBucketIntelligentTieringConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBucketInventoryConfigurationCommand: (
  input: DeleteBucketInventoryConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBucketLifecycleCommand: (
  input: DeleteBucketLifecycleCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBucketMetadataTableConfigurationCommand: (
  input: DeleteBucketMetadataTableConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBucketMetricsConfigurationCommand: (
  input: DeleteBucketMetricsConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBucketOwnershipControlsCommand: (
  input: DeleteBucketOwnershipControlsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBucketPolicyCommand: (
  input: DeleteBucketPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBucketReplicationCommand: (
  input: DeleteBucketReplicationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBucketTaggingCommand: (
  input: DeleteBucketTaggingCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteBucketWebsiteCommand: (
  input: DeleteBucketWebsiteCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteObjectCommand: (
  input: DeleteObjectCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteObjectsCommand: (
  input: DeleteObjectsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeleteObjectTaggingCommand: (
  input: DeleteObjectTaggingCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_DeletePublicAccessBlockCommand: (
  input: DeletePublicAccessBlockCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketAccelerateConfigurationCommand: (
  input: GetBucketAccelerateConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketAclCommand: (
  input: GetBucketAclCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketAnalyticsConfigurationCommand: (
  input: GetBucketAnalyticsConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketCorsCommand: (
  input: GetBucketCorsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketEncryptionCommand: (
  input: GetBucketEncryptionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketIntelligentTieringConfigurationCommand: (
  input: GetBucketIntelligentTieringConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketInventoryConfigurationCommand: (
  input: GetBucketInventoryConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketLifecycleConfigurationCommand: (
  input: GetBucketLifecycleConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketLocationCommand: (
  input: GetBucketLocationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketLoggingCommand: (
  input: GetBucketLoggingCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketMetadataTableConfigurationCommand: (
  input: GetBucketMetadataTableConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketMetricsConfigurationCommand: (
  input: GetBucketMetricsConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketNotificationConfigurationCommand: (
  input: GetBucketNotificationConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketOwnershipControlsCommand: (
  input: GetBucketOwnershipControlsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketPolicyCommand: (
  input: GetBucketPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketPolicyStatusCommand: (
  input: GetBucketPolicyStatusCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketReplicationCommand: (
  input: GetBucketReplicationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketRequestPaymentCommand: (
  input: GetBucketRequestPaymentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketTaggingCommand: (
  input: GetBucketTaggingCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketVersioningCommand: (
  input: GetBucketVersioningCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetBucketWebsiteCommand: (
  input: GetBucketWebsiteCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetObjectCommand: (
  input: GetObjectCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetObjectAclCommand: (
  input: GetObjectAclCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetObjectAttributesCommand: (
  input: GetObjectAttributesCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetObjectLegalHoldCommand: (
  input: GetObjectLegalHoldCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetObjectLockConfigurationCommand: (
  input: GetObjectLockConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetObjectRetentionCommand: (
  input: GetObjectRetentionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetObjectTaggingCommand: (
  input: GetObjectTaggingCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetObjectTorrentCommand: (
  input: GetObjectTorrentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_GetPublicAccessBlockCommand: (
  input: GetPublicAccessBlockCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_HeadBucketCommand: (
  input: HeadBucketCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_HeadObjectCommand: (
  input: HeadObjectCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListBucketAnalyticsConfigurationsCommand: (
  input: ListBucketAnalyticsConfigurationsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListBucketIntelligentTieringConfigurationsCommand: (
  input: ListBucketIntelligentTieringConfigurationsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListBucketInventoryConfigurationsCommand: (
  input: ListBucketInventoryConfigurationsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListBucketMetricsConfigurationsCommand: (
  input: ListBucketMetricsConfigurationsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListBucketsCommand: (
  input: ListBucketsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListDirectoryBucketsCommand: (
  input: ListDirectoryBucketsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListMultipartUploadsCommand: (
  input: ListMultipartUploadsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListObjectsCommand: (
  input: ListObjectsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListObjectsV2Command: (
  input: ListObjectsV2CommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListObjectVersionsCommand: (
  input: ListObjectVersionsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_ListPartsCommand: (
  input: ListPartsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketAccelerateConfigurationCommand: (
  input: PutBucketAccelerateConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketAclCommand: (
  input: PutBucketAclCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketAnalyticsConfigurationCommand: (
  input: PutBucketAnalyticsConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketCorsCommand: (
  input: PutBucketCorsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketEncryptionCommand: (
  input: PutBucketEncryptionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketIntelligentTieringConfigurationCommand: (
  input: PutBucketIntelligentTieringConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketInventoryConfigurationCommand: (
  input: PutBucketInventoryConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketLifecycleConfigurationCommand: (
  input: PutBucketLifecycleConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketLoggingCommand: (
  input: PutBucketLoggingCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketMetricsConfigurationCommand: (
  input: PutBucketMetricsConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketNotificationConfigurationCommand: (
  input: PutBucketNotificationConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketOwnershipControlsCommand: (
  input: PutBucketOwnershipControlsCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketPolicyCommand: (
  input: PutBucketPolicyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketReplicationCommand: (
  input: PutBucketReplicationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketRequestPaymentCommand: (
  input: PutBucketRequestPaymentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketTaggingCommand: (
  input: PutBucketTaggingCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketVersioningCommand: (
  input: PutBucketVersioningCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutBucketWebsiteCommand: (
  input: PutBucketWebsiteCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutObjectCommand: (
  input: PutObjectCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutObjectAclCommand: (
  input: PutObjectAclCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutObjectLegalHoldCommand: (
  input: PutObjectLegalHoldCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutObjectLockConfigurationCommand: (
  input: PutObjectLockConfigurationCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutObjectRetentionCommand: (
  input: PutObjectRetentionCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutObjectTaggingCommand: (
  input: PutObjectTaggingCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_PutPublicAccessBlockCommand: (
  input: PutPublicAccessBlockCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_RenameObjectCommand: (
  input: RenameObjectCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_RestoreObjectCommand: (
  input: RestoreObjectCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_SelectObjectContentCommand: (
  input: SelectObjectContentCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UploadPartCommand: (
  input: UploadPartCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_UploadPartCopyCommand: (
  input: UploadPartCopyCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const se_WriteGetObjectResponseCommand: (
  input: WriteGetObjectResponseCommandInput,
  context: __SerdeContext
) => Promise<__HttpRequest>;
export declare const de_AbortMultipartUploadCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<AbortMultipartUploadCommandOutput>;
export declare const de_CompleteMultipartUploadCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CompleteMultipartUploadCommandOutput>;
export declare const de_CopyObjectCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CopyObjectCommandOutput>;
export declare const de_CreateBucketCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateBucketCommandOutput>;
export declare const de_CreateBucketMetadataTableConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateBucketMetadataTableConfigurationCommandOutput>;
export declare const de_CreateMultipartUploadCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateMultipartUploadCommandOutput>;
export declare const de_CreateSessionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<CreateSessionCommandOutput>;
export declare const de_DeleteBucketCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBucketCommandOutput>;
export declare const de_DeleteBucketAnalyticsConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBucketAnalyticsConfigurationCommandOutput>;
export declare const de_DeleteBucketCorsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBucketCorsCommandOutput>;
export declare const de_DeleteBucketEncryptionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBucketEncryptionCommandOutput>;
export declare const de_DeleteBucketIntelligentTieringConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBucketIntelligentTieringConfigurationCommandOutput>;
export declare const de_DeleteBucketInventoryConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBucketInventoryConfigurationCommandOutput>;
export declare const de_DeleteBucketLifecycleCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBucketLifecycleCommandOutput>;
export declare const de_DeleteBucketMetadataTableConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBucketMetadataTableConfigurationCommandOutput>;
export declare const de_DeleteBucketMetricsConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBucketMetricsConfigurationCommandOutput>;
export declare const de_DeleteBucketOwnershipControlsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBucketOwnershipControlsCommandOutput>;
export declare const de_DeleteBucketPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBucketPolicyCommandOutput>;
export declare const de_DeleteBucketReplicationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBucketReplicationCommandOutput>;
export declare const de_DeleteBucketTaggingCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBucketTaggingCommandOutput>;
export declare const de_DeleteBucketWebsiteCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteBucketWebsiteCommandOutput>;
export declare const de_DeleteObjectCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteObjectCommandOutput>;
export declare const de_DeleteObjectsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteObjectsCommandOutput>;
export declare const de_DeleteObjectTaggingCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeleteObjectTaggingCommandOutput>;
export declare const de_DeletePublicAccessBlockCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<DeletePublicAccessBlockCommandOutput>;
export declare const de_GetBucketAccelerateConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketAccelerateConfigurationCommandOutput>;
export declare const de_GetBucketAclCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketAclCommandOutput>;
export declare const de_GetBucketAnalyticsConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketAnalyticsConfigurationCommandOutput>;
export declare const de_GetBucketCorsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketCorsCommandOutput>;
export declare const de_GetBucketEncryptionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketEncryptionCommandOutput>;
export declare const de_GetBucketIntelligentTieringConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketIntelligentTieringConfigurationCommandOutput>;
export declare const de_GetBucketInventoryConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketInventoryConfigurationCommandOutput>;
export declare const de_GetBucketLifecycleConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketLifecycleConfigurationCommandOutput>;
export declare const de_GetBucketLocationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketLocationCommandOutput>;
export declare const de_GetBucketLoggingCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketLoggingCommandOutput>;
export declare const de_GetBucketMetadataTableConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketMetadataTableConfigurationCommandOutput>;
export declare const de_GetBucketMetricsConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketMetricsConfigurationCommandOutput>;
export declare const de_GetBucketNotificationConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketNotificationConfigurationCommandOutput>;
export declare const de_GetBucketOwnershipControlsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketOwnershipControlsCommandOutput>;
export declare const de_GetBucketPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketPolicyCommandOutput>;
export declare const de_GetBucketPolicyStatusCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketPolicyStatusCommandOutput>;
export declare const de_GetBucketReplicationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketReplicationCommandOutput>;
export declare const de_GetBucketRequestPaymentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketRequestPaymentCommandOutput>;
export declare const de_GetBucketTaggingCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketTaggingCommandOutput>;
export declare const de_GetBucketVersioningCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketVersioningCommandOutput>;
export declare const de_GetBucketWebsiteCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetBucketWebsiteCommandOutput>;
export declare const de_GetObjectCommand: (
  output: __HttpResponse,
  context: __SerdeContext & __SdkStreamSerdeContext
) => Promise<GetObjectCommandOutput>;
export declare const de_GetObjectAclCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetObjectAclCommandOutput>;
export declare const de_GetObjectAttributesCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetObjectAttributesCommandOutput>;
export declare const de_GetObjectLegalHoldCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetObjectLegalHoldCommandOutput>;
export declare const de_GetObjectLockConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetObjectLockConfigurationCommandOutput>;
export declare const de_GetObjectRetentionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetObjectRetentionCommandOutput>;
export declare const de_GetObjectTaggingCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetObjectTaggingCommandOutput>;
export declare const de_GetObjectTorrentCommand: (
  output: __HttpResponse,
  context: __SerdeContext & __SdkStreamSerdeContext
) => Promise<GetObjectTorrentCommandOutput>;
export declare const de_GetPublicAccessBlockCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<GetPublicAccessBlockCommandOutput>;
export declare const de_HeadBucketCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<HeadBucketCommandOutput>;
export declare const de_HeadObjectCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<HeadObjectCommandOutput>;
export declare const de_ListBucketAnalyticsConfigurationsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListBucketAnalyticsConfigurationsCommandOutput>;
export declare const de_ListBucketIntelligentTieringConfigurationsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListBucketIntelligentTieringConfigurationsCommandOutput>;
export declare const de_ListBucketInventoryConfigurationsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListBucketInventoryConfigurationsCommandOutput>;
export declare const de_ListBucketMetricsConfigurationsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListBucketMetricsConfigurationsCommandOutput>;
export declare const de_ListBucketsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListBucketsCommandOutput>;
export declare const de_ListDirectoryBucketsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListDirectoryBucketsCommandOutput>;
export declare const de_ListMultipartUploadsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListMultipartUploadsCommandOutput>;
export declare const de_ListObjectsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListObjectsCommandOutput>;
export declare const de_ListObjectsV2Command: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListObjectsV2CommandOutput>;
export declare const de_ListObjectVersionsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListObjectVersionsCommandOutput>;
export declare const de_ListPartsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<ListPartsCommandOutput>;
export declare const de_PutBucketAccelerateConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketAccelerateConfigurationCommandOutput>;
export declare const de_PutBucketAclCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketAclCommandOutput>;
export declare const de_PutBucketAnalyticsConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketAnalyticsConfigurationCommandOutput>;
export declare const de_PutBucketCorsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketCorsCommandOutput>;
export declare const de_PutBucketEncryptionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketEncryptionCommandOutput>;
export declare const de_PutBucketIntelligentTieringConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketIntelligentTieringConfigurationCommandOutput>;
export declare const de_PutBucketInventoryConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketInventoryConfigurationCommandOutput>;
export declare const de_PutBucketLifecycleConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketLifecycleConfigurationCommandOutput>;
export declare const de_PutBucketLoggingCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketLoggingCommandOutput>;
export declare const de_PutBucketMetricsConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketMetricsConfigurationCommandOutput>;
export declare const de_PutBucketNotificationConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketNotificationConfigurationCommandOutput>;
export declare const de_PutBucketOwnershipControlsCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketOwnershipControlsCommandOutput>;
export declare const de_PutBucketPolicyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketPolicyCommandOutput>;
export declare const de_PutBucketReplicationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketReplicationCommandOutput>;
export declare const de_PutBucketRequestPaymentCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketRequestPaymentCommandOutput>;
export declare const de_PutBucketTaggingCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketTaggingCommandOutput>;
export declare const de_PutBucketVersioningCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketVersioningCommandOutput>;
export declare const de_PutBucketWebsiteCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutBucketWebsiteCommandOutput>;
export declare const de_PutObjectCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutObjectCommandOutput>;
export declare const de_PutObjectAclCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutObjectAclCommandOutput>;
export declare const de_PutObjectLegalHoldCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutObjectLegalHoldCommandOutput>;
export declare const de_PutObjectLockConfigurationCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutObjectLockConfigurationCommandOutput>;
export declare const de_PutObjectRetentionCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutObjectRetentionCommandOutput>;
export declare const de_PutObjectTaggingCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutObjectTaggingCommandOutput>;
export declare const de_PutPublicAccessBlockCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<PutPublicAccessBlockCommandOutput>;
export declare const de_RenameObjectCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<RenameObjectCommandOutput>;
export declare const de_RestoreObjectCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<RestoreObjectCommandOutput>;
export declare const de_SelectObjectContentCommand: (
  output: __HttpResponse,
  context: __SerdeContext & __EventStreamSerdeContext
) => Promise<SelectObjectContentCommandOutput>;
export declare const de_UploadPartCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UploadPartCommandOutput>;
export declare const de_UploadPartCopyCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<UploadPartCopyCommandOutput>;
export declare const de_WriteGetObjectResponseCommand: (
  output: __HttpResponse,
  context: __SerdeContext
) => Promise<WriteGetObjectResponseCommandOutput>;
