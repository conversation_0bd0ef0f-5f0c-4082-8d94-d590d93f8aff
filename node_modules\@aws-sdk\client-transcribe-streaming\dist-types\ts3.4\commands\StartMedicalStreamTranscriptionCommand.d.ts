import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  StartMedicalStreamTranscriptionRequest,
  StartMedicalStreamTranscriptionResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  TranscribeStreamingClientResolvedConfig,
} from "../TranscribeStreamingClient";
export { __MetadataBearer };
export { $Command };
export interface StartMedicalStreamTranscriptionCommandInput
  extends StartMedicalStreamTranscriptionRequest {}
export interface StartMedicalStreamTranscriptionCommandOutput
  extends StartMedicalStreamTranscriptionResponse,
    __MetadataBearer {}
declare const StartMedicalStreamTranscriptionCommand_base: {
  new (
    input: StartMedicalStreamTranscriptionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartMedicalStreamTranscriptionCommandInput,
    StartMedicalStreamTranscriptionCommandOutput,
    TranscribeStreamingClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StartMedicalStreamTranscriptionCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartMedicalStreamTranscriptionCommandInput,
    StartMedicalStreamTranscriptionCommandOutput,
    TranscribeStreamingClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StartMedicalStreamTranscriptionCommand extends StartMedicalStreamTranscriptionCommand_base {
  protected static __types: {
    api: {
      input: StartMedicalStreamTranscriptionRequest;
      output: StartMedicalStreamTranscriptionResponse;
    };
    sdk: {
      input: StartMedicalStreamTranscriptionCommandInput;
      output: StartMedicalStreamTranscriptionCommandOutput;
    };
  };
}
