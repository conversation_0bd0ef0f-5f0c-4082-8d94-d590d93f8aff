import { HttpRequest } from "@smithy/protocol-http";
export const websocketPortMiddleware = (options) => (next) => (args) => {
    const { request } = args;
    if (HttpRequest.isInstance(request) && options.requestHandler.metadata?.handlerProtocol?.includes("websocket")) {
        request.hostname = `${request.hostname}:8443`;
        request.headers.host = request.hostname;
    }
    return next(args);
};
export const websocketPortMiddlewareOptions = {
    name: "websocketPortMiddleware",
    tags: ["WEBSOCKET", "EVENT_STREAM", "PORT"],
    relation: "after",
    toMiddleware: "eventStreamHeaderMiddleware",
    override: true,
};
