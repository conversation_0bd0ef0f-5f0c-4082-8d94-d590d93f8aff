import { createAggregatedClient } from "@smithy/smithy-client";
import { GetMedicalScribeStreamCommand, } from "./commands/GetMedicalScribeStreamCommand";
import { StartCallAnalyticsStreamTranscriptionCommand, } from "./commands/StartCallAnalyticsStreamTranscriptionCommand";
import { StartMedicalScribeStreamCommand, } from "./commands/StartMedicalScribeStreamCommand";
import { StartMedicalStreamTranscriptionCommand, } from "./commands/StartMedicalStreamTranscriptionCommand";
import { StartStreamTranscriptionCommand, } from "./commands/StartStreamTranscriptionCommand";
import { TranscribeStreamingClient } from "./TranscribeStreamingClient";
const commands = {
    GetMedicalScribeStreamCommand,
    StartCallAnalyticsStreamTranscriptionCommand,
    StartMedicalScribeStreamCommand,
    StartMedicalStreamTranscriptionCommand,
    StartStreamTranscriptionCommand,
};
export class TranscribeStreaming extends TranscribeStreamingClient {
}
createAggregatedClient(commands, TranscribeStreaming);
