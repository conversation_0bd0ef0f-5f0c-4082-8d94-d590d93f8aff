import { loadRestJsonErrorCode, parseJsonBody as parseBody, parseJsonErrorBody as parseErrorBody } from "@aws-sdk/core";
import { requestBuilder as rb } from "@smithy/core";
import { _json, collectBody, decorateServiceException as __decorateServiceException, expectBoolean as __expectBoolean, expectInt32 as __expectInt32, expectLong as __expectLong, expectNonNull as __expectNonNull, expectNumber as __expectNumber, expectObject as __expectObject, expectString as __expectString, isSerializableHeaderValue, limitedParseDouble as __limitedParseDouble, map, parseBoolean as __parseBoolean, parseEpochTimestamp as __parseEpochTimestamp, strictParseInt32 as __strictParseInt32, take, withBaseException, } from "@smithy/smithy-client";
import { AudioStream, BadRequestException, ConflictException, InternalFailureException, LimitExceededException, MedicalScribeInputStream, ResourceNotFoundException, ServiceUnavailableException, } from "../models/models_0";
import { TranscribeStreamingServiceException as __BaseException } from "../models/TranscribeStreamingServiceException";
export const se_GetMedicalScribeStreamCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = {};
    b.bp("/medical-scribe-stream/{SessionId}");
    b.p("SessionId", () => input.SessionId, "{SessionId}", false);
    let body;
    b.m("GET").h(headers).b(body);
    return b.build();
};
export const se_StartCallAnalyticsStreamTranscriptionCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = map({}, isSerializableHeaderValue, {
        "content-type": "application/json",
        [_xatlc]: input[_LC],
        [_xatsr]: [() => isSerializableHeaderValue(input[_MSRH]), () => input[_MSRH].toString()],
        [_xatme]: input[_ME],
        [_xatvn]: input[_VN],
        [_xatsi]: input[_SI],
        [_xatvfn]: input[_VFN],
        [_xatvfm]: input[_VFM],
        [_xatlmn]: input[_LMN],
        [_xateprs]: [() => isSerializableHeaderValue(input[_EPRS]), () => input[_EPRS].toString()],
        [_xatprs]: input[_PRS],
        [_xatcit]: input[_CIT],
        [_xatcrt]: input[_CRT],
        [_xatpet]: input[_PET],
    });
    b.bp("/call-analytics-stream-transcription");
    let body;
    if (input.AudioStream !== undefined) {
        body = se_AudioStream(input.AudioStream, context);
    }
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_StartMedicalScribeStreamCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = map({}, isSerializableHeaderValue, {
        "content-type": "application/json",
        [_xatsi]: input[_SI],
        [_xatlc]: input[_LC],
        [_xatsr]: [() => isSerializableHeaderValue(input[_MSRH]), () => input[_MSRH].toString()],
        [_xatme]: input[_ME],
    });
    b.bp("/medical-scribe-stream");
    let body;
    if (input.InputStream !== undefined) {
        body = se_MedicalScribeInputStream(input.InputStream, context);
    }
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_StartMedicalStreamTranscriptionCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = map({}, isSerializableHeaderValue, {
        "content-type": "application/json",
        [_xatlc]: input[_LC],
        [_xatsr]: [() => isSerializableHeaderValue(input[_MSRH]), () => input[_MSRH].toString()],
        [_xatme]: input[_ME],
        [_xatvn]: input[_VN],
        [_xats]: input[_S],
        [_xatt]: input[_T],
        [_xatssl]: [() => isSerializableHeaderValue(input[_SSL]), () => input[_SSL].toString()],
        [_xatsi]: input[_SI],
        [_xateci]: [() => isSerializableHeaderValue(input[_ECI]), () => input[_ECI].toString()],
        [_xatnoc]: [() => isSerializableHeaderValue(input[_NOC]), () => input[_NOC].toString()],
        [_xatcit]: input[_CIT],
    });
    b.bp("/medical-stream-transcription");
    let body;
    if (input.AudioStream !== undefined) {
        body = se_AudioStream(input.AudioStream, context);
    }
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const se_StartStreamTranscriptionCommand = async (input, context) => {
    const b = rb(input, context);
    const headers = map({}, isSerializableHeaderValue, {
        "content-type": "application/json",
        [_xatlc]: input[_LC],
        [_xatsr]: [() => isSerializableHeaderValue(input[_MSRH]), () => input[_MSRH].toString()],
        [_xatme]: input[_ME],
        [_xatvn]: input[_VN],
        [_xatsi]: input[_SI],
        [_xatvfn]: input[_VFN],
        [_xatvfm]: input[_VFM],
        [_xatssl]: [() => isSerializableHeaderValue(input[_SSL]), () => input[_SSL].toString()],
        [_xateci]: [() => isSerializableHeaderValue(input[_ECI]), () => input[_ECI].toString()],
        [_xatnoc]: [() => isSerializableHeaderValue(input[_NOC]), () => input[_NOC].toString()],
        [_xateprs]: [() => isSerializableHeaderValue(input[_EPRS]), () => input[_EPRS].toString()],
        [_xatprs]: input[_PRS],
        [_xatcit]: input[_CIT],
        [_xatcrt]: input[_CRT],
        [_xatpet]: input[_PET],
        [_xatlmn]: input[_LMN],
        [_xatil]: [() => isSerializableHeaderValue(input[_IL]), () => input[_IL].toString()],
        [_xatlo]: input[_LO],
        [_xatpl]: input[_PL],
        [_xatiml]: [() => isSerializableHeaderValue(input[_IML]), () => input[_IML].toString()],
        [_xatvn_]: input[_VNo],
        [_xatvfn_]: input[_VFNo],
    });
    b.bp("/stream-transcription");
    let body;
    if (input.AudioStream !== undefined) {
        body = se_AudioStream(input.AudioStream, context);
    }
    b.m("POST").h(headers).b(body);
    return b.build();
};
export const de_GetMedicalScribeStreamCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
    });
    const data = __expectNonNull(__expectObject(await parseBody(output.body, context)), "body");
    const doc = take(data, {
        MedicalScribeStreamDetails: (_) => de_MedicalScribeStreamDetails(_, context),
    });
    Object.assign(contents, doc);
    return contents;
};
export const de_StartCallAnalyticsStreamTranscriptionCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
        [_RI]: [, output.headers[_xari]],
        [_LC]: [, output.headers[_xatlc]],
        [_MSRH]: [() => void 0 !== output.headers[_xatsr], () => __strictParseInt32(output.headers[_xatsr])],
        [_ME]: [, output.headers[_xatme]],
        [_VN]: [, output.headers[_xatvn]],
        [_SI]: [, output.headers[_xatsi]],
        [_VFN]: [, output.headers[_xatvfn]],
        [_VFM]: [, output.headers[_xatvfm]],
        [_LMN]: [, output.headers[_xatlmn]],
        [_EPRS]: [() => void 0 !== output.headers[_xateprs], () => __parseBoolean(output.headers[_xateprs])],
        [_PRS]: [, output.headers[_xatprs]],
        [_CIT]: [, output.headers[_xatcit]],
        [_CRT]: [, output.headers[_xatcrt]],
        [_PET]: [, output.headers[_xatpet]],
    });
    const data = output.body;
    contents.CallAnalyticsTranscriptResultStream = de_CallAnalyticsTranscriptResultStream(data, context);
    return contents;
};
export const de_StartMedicalScribeStreamCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
        [_SI]: [, output.headers[_xatsi]],
        [_RI]: [, output.headers[_xari]],
        [_LC]: [, output.headers[_xatlc]],
        [_MSRH]: [() => void 0 !== output.headers[_xatsr], () => __strictParseInt32(output.headers[_xatsr])],
        [_ME]: [, output.headers[_xatme]],
    });
    const data = output.body;
    contents.ResultStream = de_MedicalScribeResultStream(data, context);
    return contents;
};
export const de_StartMedicalStreamTranscriptionCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
        [_RI]: [, output.headers[_xari]],
        [_LC]: [, output.headers[_xatlc]],
        [_MSRH]: [() => void 0 !== output.headers[_xatsr], () => __strictParseInt32(output.headers[_xatsr])],
        [_ME]: [, output.headers[_xatme]],
        [_VN]: [, output.headers[_xatvn]],
        [_S]: [, output.headers[_xats]],
        [_T]: [, output.headers[_xatt]],
        [_SSL]: [() => void 0 !== output.headers[_xatssl], () => __parseBoolean(output.headers[_xatssl])],
        [_SI]: [, output.headers[_xatsi]],
        [_ECI]: [() => void 0 !== output.headers[_xateci], () => __parseBoolean(output.headers[_xateci])],
        [_NOC]: [() => void 0 !== output.headers[_xatnoc], () => __strictParseInt32(output.headers[_xatnoc])],
        [_CIT]: [, output.headers[_xatcit]],
    });
    const data = output.body;
    contents.TranscriptResultStream = de_MedicalTranscriptResultStream(data, context);
    return contents;
};
export const de_StartStreamTranscriptionCommand = async (output, context) => {
    if (output.statusCode !== 200 && output.statusCode >= 300) {
        return de_CommandError(output, context);
    }
    const contents = map({
        $metadata: deserializeMetadata(output),
        [_RI]: [, output.headers[_xari]],
        [_LC]: [, output.headers[_xatlc]],
        [_MSRH]: [() => void 0 !== output.headers[_xatsr], () => __strictParseInt32(output.headers[_xatsr])],
        [_ME]: [, output.headers[_xatme]],
        [_VN]: [, output.headers[_xatvn]],
        [_SI]: [, output.headers[_xatsi]],
        [_VFN]: [, output.headers[_xatvfn]],
        [_VFM]: [, output.headers[_xatvfm]],
        [_SSL]: [() => void 0 !== output.headers[_xatssl], () => __parseBoolean(output.headers[_xatssl])],
        [_ECI]: [() => void 0 !== output.headers[_xateci], () => __parseBoolean(output.headers[_xateci])],
        [_NOC]: [() => void 0 !== output.headers[_xatnoc], () => __strictParseInt32(output.headers[_xatnoc])],
        [_EPRS]: [() => void 0 !== output.headers[_xateprs], () => __parseBoolean(output.headers[_xateprs])],
        [_PRS]: [, output.headers[_xatprs]],
        [_CIT]: [, output.headers[_xatcit]],
        [_CRT]: [, output.headers[_xatcrt]],
        [_PET]: [, output.headers[_xatpet]],
        [_LMN]: [, output.headers[_xatlmn]],
        [_IL]: [() => void 0 !== output.headers[_xatil], () => __parseBoolean(output.headers[_xatil])],
        [_LO]: [, output.headers[_xatlo]],
        [_PL]: [, output.headers[_xatpl]],
        [_IML]: [() => void 0 !== output.headers[_xatiml], () => __parseBoolean(output.headers[_xatiml])],
        [_VNo]: [, output.headers[_xatvn_]],
        [_VFNo]: [, output.headers[_xatvfn_]],
    });
    const data = output.body;
    contents.TranscriptResultStream = de_TranscriptResultStream(data, context);
    return contents;
};
const de_CommandError = async (output, context) => {
    const parsedOutput = {
        ...output,
        body: await parseErrorBody(output.body, context),
    };
    const errorCode = loadRestJsonErrorCode(output, parsedOutput.body);
    switch (errorCode) {
        case "BadRequestException":
        case "com.amazonaws.transcribestreaming#BadRequestException":
            throw await de_BadRequestExceptionRes(parsedOutput, context);
        case "InternalFailureException":
        case "com.amazonaws.transcribestreaming#InternalFailureException":
            throw await de_InternalFailureExceptionRes(parsedOutput, context);
        case "LimitExceededException":
        case "com.amazonaws.transcribestreaming#LimitExceededException":
            throw await de_LimitExceededExceptionRes(parsedOutput, context);
        case "ResourceNotFoundException":
        case "com.amazonaws.transcribestreaming#ResourceNotFoundException":
            throw await de_ResourceNotFoundExceptionRes(parsedOutput, context);
        case "ConflictException":
        case "com.amazonaws.transcribestreaming#ConflictException":
            throw await de_ConflictExceptionRes(parsedOutput, context);
        case "ServiceUnavailableException":
        case "com.amazonaws.transcribestreaming#ServiceUnavailableException":
            throw await de_ServiceUnavailableExceptionRes(parsedOutput, context);
        default:
            const parsedBody = parsedOutput.body;
            return throwDefaultError({
                output,
                parsedBody,
                errorCode,
            });
    }
};
const throwDefaultError = withBaseException(__BaseException);
const de_BadRequestExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        Message: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new BadRequestException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_ConflictExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        Message: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new ConflictException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_InternalFailureExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        Message: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new InternalFailureException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_LimitExceededExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        Message: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new LimitExceededException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_ResourceNotFoundExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        Message: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new ResourceNotFoundException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const de_ServiceUnavailableExceptionRes = async (parsedOutput, context) => {
    const contents = map({});
    const data = parsedOutput.body;
    const doc = take(data, {
        Message: __expectString,
    });
    Object.assign(contents, doc);
    const exception = new ServiceUnavailableException({
        $metadata: deserializeMetadata(parsedOutput),
        ...contents,
    });
    return __decorateServiceException(exception, parsedOutput.body);
};
const se_AudioStream = (input, context) => {
    const eventMarshallingVisitor = (event) => AudioStream.visit(event, {
        AudioEvent: (value) => se_AudioEvent_event(value, context),
        ConfigurationEvent: (value) => se_ConfigurationEvent_event(value, context),
        _: (value) => value,
    });
    return context.eventStreamMarshaller.serialize(input, eventMarshallingVisitor);
};
const se_MedicalScribeInputStream = (input, context) => {
    const eventMarshallingVisitor = (event) => MedicalScribeInputStream.visit(event, {
        AudioEvent: (value) => se_MedicalScribeAudioEvent_event(value, context),
        SessionControlEvent: (value) => se_MedicalScribeSessionControlEvent_event(value, context),
        ConfigurationEvent: (value) => se_MedicalScribeConfigurationEvent_event(value, context),
        _: (value) => value,
    });
    return context.eventStreamMarshaller.serialize(input, eventMarshallingVisitor);
};
const se_AudioEvent_event = (input, context) => {
    const headers = {
        ":event-type": { type: "string", value: "AudioEvent" },
        ":message-type": { type: "string", value: "event" },
        ":content-type": { type: "string", value: "application/octet-stream" },
    };
    let body = new Uint8Array();
    if (input.AudioChunk != null) {
        body = input.AudioChunk;
    }
    return { headers, body };
};
const se_ConfigurationEvent_event = (input, context) => {
    const headers = {
        ":event-type": { type: "string", value: "ConfigurationEvent" },
        ":message-type": { type: "string", value: "event" },
        ":content-type": { type: "string", value: "application/json" },
    };
    let body = new Uint8Array();
    body = _json(input);
    body = context.utf8Decoder(JSON.stringify(body));
    return { headers, body };
};
const se_MedicalScribeAudioEvent_event = (input, context) => {
    const headers = {
        ":event-type": { type: "string", value: "AudioEvent" },
        ":message-type": { type: "string", value: "event" },
        ":content-type": { type: "string", value: "application/octet-stream" },
    };
    let body = new Uint8Array();
    if (input.AudioChunk != null) {
        body = input.AudioChunk;
    }
    return { headers, body };
};
const se_MedicalScribeConfigurationEvent_event = (input, context) => {
    const headers = {
        ":event-type": { type: "string", value: "ConfigurationEvent" },
        ":message-type": { type: "string", value: "event" },
        ":content-type": { type: "string", value: "application/json" },
    };
    let body = new Uint8Array();
    body = _json(input);
    body = context.utf8Decoder(JSON.stringify(body));
    return { headers, body };
};
const se_MedicalScribeSessionControlEvent_event = (input, context) => {
    const headers = {
        ":event-type": { type: "string", value: "SessionControlEvent" },
        ":message-type": { type: "string", value: "event" },
        ":content-type": { type: "string", value: "application/json" },
    };
    let body = new Uint8Array();
    body = _json(input);
    body = context.utf8Decoder(JSON.stringify(body));
    return { headers, body };
};
const de_CallAnalyticsTranscriptResultStream = (output, context) => {
    return context.eventStreamMarshaller.deserialize(output, async (event) => {
        if (event["UtteranceEvent"] != null) {
            return {
                UtteranceEvent: await de_UtteranceEvent_event(event["UtteranceEvent"], context),
            };
        }
        if (event["CategoryEvent"] != null) {
            return {
                CategoryEvent: await de_CategoryEvent_event(event["CategoryEvent"], context),
            };
        }
        if (event["BadRequestException"] != null) {
            return {
                BadRequestException: await de_BadRequestException_event(event["BadRequestException"], context),
            };
        }
        if (event["LimitExceededException"] != null) {
            return {
                LimitExceededException: await de_LimitExceededException_event(event["LimitExceededException"], context),
            };
        }
        if (event["InternalFailureException"] != null) {
            return {
                InternalFailureException: await de_InternalFailureException_event(event["InternalFailureException"], context),
            };
        }
        if (event["ConflictException"] != null) {
            return {
                ConflictException: await de_ConflictException_event(event["ConflictException"], context),
            };
        }
        if (event["ServiceUnavailableException"] != null) {
            return {
                ServiceUnavailableException: await de_ServiceUnavailableException_event(event["ServiceUnavailableException"], context),
            };
        }
        return { $unknown: event };
    });
};
const de_MedicalScribeResultStream = (output, context) => {
    return context.eventStreamMarshaller.deserialize(output, async (event) => {
        if (event["TranscriptEvent"] != null) {
            return {
                TranscriptEvent: await de_MedicalScribeTranscriptEvent_event(event["TranscriptEvent"], context),
            };
        }
        if (event["BadRequestException"] != null) {
            return {
                BadRequestException: await de_BadRequestException_event(event["BadRequestException"], context),
            };
        }
        if (event["LimitExceededException"] != null) {
            return {
                LimitExceededException: await de_LimitExceededException_event(event["LimitExceededException"], context),
            };
        }
        if (event["InternalFailureException"] != null) {
            return {
                InternalFailureException: await de_InternalFailureException_event(event["InternalFailureException"], context),
            };
        }
        if (event["ConflictException"] != null) {
            return {
                ConflictException: await de_ConflictException_event(event["ConflictException"], context),
            };
        }
        if (event["ServiceUnavailableException"] != null) {
            return {
                ServiceUnavailableException: await de_ServiceUnavailableException_event(event["ServiceUnavailableException"], context),
            };
        }
        return { $unknown: event };
    });
};
const de_MedicalTranscriptResultStream = (output, context) => {
    return context.eventStreamMarshaller.deserialize(output, async (event) => {
        if (event["TranscriptEvent"] != null) {
            return {
                TranscriptEvent: await de_MedicalTranscriptEvent_event(event["TranscriptEvent"], context),
            };
        }
        if (event["BadRequestException"] != null) {
            return {
                BadRequestException: await de_BadRequestException_event(event["BadRequestException"], context),
            };
        }
        if (event["LimitExceededException"] != null) {
            return {
                LimitExceededException: await de_LimitExceededException_event(event["LimitExceededException"], context),
            };
        }
        if (event["InternalFailureException"] != null) {
            return {
                InternalFailureException: await de_InternalFailureException_event(event["InternalFailureException"], context),
            };
        }
        if (event["ConflictException"] != null) {
            return {
                ConflictException: await de_ConflictException_event(event["ConflictException"], context),
            };
        }
        if (event["ServiceUnavailableException"] != null) {
            return {
                ServiceUnavailableException: await de_ServiceUnavailableException_event(event["ServiceUnavailableException"], context),
            };
        }
        return { $unknown: event };
    });
};
const de_TranscriptResultStream = (output, context) => {
    return context.eventStreamMarshaller.deserialize(output, async (event) => {
        if (event["TranscriptEvent"] != null) {
            return {
                TranscriptEvent: await de_TranscriptEvent_event(event["TranscriptEvent"], context),
            };
        }
        if (event["BadRequestException"] != null) {
            return {
                BadRequestException: await de_BadRequestException_event(event["BadRequestException"], context),
            };
        }
        if (event["LimitExceededException"] != null) {
            return {
                LimitExceededException: await de_LimitExceededException_event(event["LimitExceededException"], context),
            };
        }
        if (event["InternalFailureException"] != null) {
            return {
                InternalFailureException: await de_InternalFailureException_event(event["InternalFailureException"], context),
            };
        }
        if (event["ConflictException"] != null) {
            return {
                ConflictException: await de_ConflictException_event(event["ConflictException"], context),
            };
        }
        if (event["ServiceUnavailableException"] != null) {
            return {
                ServiceUnavailableException: await de_ServiceUnavailableException_event(event["ServiceUnavailableException"], context),
            };
        }
        return { $unknown: event };
    });
};
const de_BadRequestException_event = async (output, context) => {
    const parsedOutput = {
        ...output,
        body: await parseBody(output.body, context),
    };
    return de_BadRequestExceptionRes(parsedOutput, context);
};
const de_CategoryEvent_event = async (output, context) => {
    const contents = {};
    const data = await parseBody(output.body, context);
    Object.assign(contents, _json(data));
    return contents;
};
const de_ConflictException_event = async (output, context) => {
    const parsedOutput = {
        ...output,
        body: await parseBody(output.body, context),
    };
    return de_ConflictExceptionRes(parsedOutput, context);
};
const de_InternalFailureException_event = async (output, context) => {
    const parsedOutput = {
        ...output,
        body: await parseBody(output.body, context),
    };
    return de_InternalFailureExceptionRes(parsedOutput, context);
};
const de_LimitExceededException_event = async (output, context) => {
    const parsedOutput = {
        ...output,
        body: await parseBody(output.body, context),
    };
    return de_LimitExceededExceptionRes(parsedOutput, context);
};
const de_MedicalScribeTranscriptEvent_event = async (output, context) => {
    const contents = {};
    const data = await parseBody(output.body, context);
    Object.assign(contents, de_MedicalScribeTranscriptEvent(data, context));
    return contents;
};
const de_MedicalTranscriptEvent_event = async (output, context) => {
    const contents = {};
    const data = await parseBody(output.body, context);
    Object.assign(contents, de_MedicalTranscriptEvent(data, context));
    return contents;
};
const de_ServiceUnavailableException_event = async (output, context) => {
    const parsedOutput = {
        ...output,
        body: await parseBody(output.body, context),
    };
    return de_ServiceUnavailableExceptionRes(parsedOutput, context);
};
const de_TranscriptEvent_event = async (output, context) => {
    const contents = {};
    const data = await parseBody(output.body, context);
    Object.assign(contents, de_TranscriptEvent(data, context));
    return contents;
};
const de_UtteranceEvent_event = async (output, context) => {
    const contents = {};
    const data = await parseBody(output.body, context);
    Object.assign(contents, de_UtteranceEvent(data, context));
    return contents;
};
const de_Alternative = (output, context) => {
    return take(output, {
        Entities: (_) => de_EntityList(_, context),
        Items: (_) => de_ItemList(_, context),
        Transcript: __expectString,
    });
};
const de_AlternativeList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_Alternative(entry, context);
    });
    return retVal;
};
const de_CallAnalyticsEntity = (output, context) => {
    return take(output, {
        BeginOffsetMillis: __expectLong,
        Category: __expectString,
        Confidence: __limitedParseDouble,
        Content: __expectString,
        EndOffsetMillis: __expectLong,
        Type: __expectString,
    });
};
const de_CallAnalyticsEntityList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_CallAnalyticsEntity(entry, context);
    });
    return retVal;
};
const de_CallAnalyticsItem = (output, context) => {
    return take(output, {
        BeginOffsetMillis: __expectLong,
        Confidence: __limitedParseDouble,
        Content: __expectString,
        EndOffsetMillis: __expectLong,
        Stable: __expectBoolean,
        Type: __expectString,
        VocabularyFilterMatch: __expectBoolean,
    });
};
const de_CallAnalyticsItemList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_CallAnalyticsItem(entry, context);
    });
    return retVal;
};
const de_Entity = (output, context) => {
    return take(output, {
        Category: __expectString,
        Confidence: __limitedParseDouble,
        Content: __expectString,
        EndTime: __limitedParseDouble,
        StartTime: __limitedParseDouble,
        Type: __expectString,
    });
};
const de_EntityList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_Entity(entry, context);
    });
    return retVal;
};
const de_Item = (output, context) => {
    return take(output, {
        Confidence: __limitedParseDouble,
        Content: __expectString,
        EndTime: __limitedParseDouble,
        Speaker: __expectString,
        Stable: __expectBoolean,
        StartTime: __limitedParseDouble,
        Type: __expectString,
        VocabularyFilterMatch: __expectBoolean,
    });
};
const de_ItemList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_Item(entry, context);
    });
    return retVal;
};
const de_LanguageIdentification = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_LanguageWithScore(entry, context);
    });
    return retVal;
};
const de_LanguageWithScore = (output, context) => {
    return take(output, {
        LanguageCode: __expectString,
        Score: __limitedParseDouble,
    });
};
const de_MedicalAlternative = (output, context) => {
    return take(output, {
        Entities: (_) => de_MedicalEntityList(_, context),
        Items: (_) => de_MedicalItemList(_, context),
        Transcript: __expectString,
    });
};
const de_MedicalAlternativeList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_MedicalAlternative(entry, context);
    });
    return retVal;
};
const de_MedicalEntity = (output, context) => {
    return take(output, {
        Category: __expectString,
        Confidence: __limitedParseDouble,
        Content: __expectString,
        EndTime: __limitedParseDouble,
        StartTime: __limitedParseDouble,
    });
};
const de_MedicalEntityList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_MedicalEntity(entry, context);
    });
    return retVal;
};
const de_MedicalItem = (output, context) => {
    return take(output, {
        Confidence: __limitedParseDouble,
        Content: __expectString,
        EndTime: __limitedParseDouble,
        Speaker: __expectString,
        StartTime: __limitedParseDouble,
        Type: __expectString,
    });
};
const de_MedicalItemList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_MedicalItem(entry, context);
    });
    return retVal;
};
const de_MedicalResult = (output, context) => {
    return take(output, {
        Alternatives: (_) => de_MedicalAlternativeList(_, context),
        ChannelId: __expectString,
        EndTime: __limitedParseDouble,
        IsPartial: __expectBoolean,
        ResultId: __expectString,
        StartTime: __limitedParseDouble,
    });
};
const de_MedicalResultList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_MedicalResult(entry, context);
    });
    return retVal;
};
const de_MedicalScribeStreamDetails = (output, context) => {
    return take(output, {
        ChannelDefinitions: _json,
        EncryptionSettings: _json,
        LanguageCode: __expectString,
        MediaEncoding: __expectString,
        MediaSampleRateHertz: __expectInt32,
        PostStreamAnalyticsResult: _json,
        PostStreamAnalyticsSettings: _json,
        ResourceAccessRoleArn: __expectString,
        SessionId: __expectString,
        StreamCreatedAt: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
        StreamEndedAt: (_) => __expectNonNull(__parseEpochTimestamp(__expectNumber(_))),
        StreamStatus: __expectString,
        VocabularyFilterMethod: __expectString,
        VocabularyFilterName: __expectString,
        VocabularyName: __expectString,
    });
};
const de_MedicalScribeTranscriptEvent = (output, context) => {
    return take(output, {
        TranscriptSegment: (_) => de_MedicalScribeTranscriptSegment(_, context),
    });
};
const de_MedicalScribeTranscriptItem = (output, context) => {
    return take(output, {
        BeginAudioTime: __limitedParseDouble,
        Confidence: __limitedParseDouble,
        Content: __expectString,
        EndAudioTime: __limitedParseDouble,
        Type: __expectString,
        VocabularyFilterMatch: __expectBoolean,
    });
};
const de_MedicalScribeTranscriptItemList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_MedicalScribeTranscriptItem(entry, context);
    });
    return retVal;
};
const de_MedicalScribeTranscriptSegment = (output, context) => {
    return take(output, {
        BeginAudioTime: __limitedParseDouble,
        ChannelId: __expectString,
        Content: __expectString,
        EndAudioTime: __limitedParseDouble,
        IsPartial: __expectBoolean,
        Items: (_) => de_MedicalScribeTranscriptItemList(_, context),
        SegmentId: __expectString,
    });
};
const de_MedicalTranscript = (output, context) => {
    return take(output, {
        Results: (_) => de_MedicalResultList(_, context),
    });
};
const de_MedicalTranscriptEvent = (output, context) => {
    return take(output, {
        Transcript: (_) => de_MedicalTranscript(_, context),
    });
};
const de_Result = (output, context) => {
    return take(output, {
        Alternatives: (_) => de_AlternativeList(_, context),
        ChannelId: __expectString,
        EndTime: __limitedParseDouble,
        IsPartial: __expectBoolean,
        LanguageCode: __expectString,
        LanguageIdentification: (_) => de_LanguageIdentification(_, context),
        ResultId: __expectString,
        StartTime: __limitedParseDouble,
    });
};
const de_ResultList = (output, context) => {
    const retVal = (output || [])
        .filter((e) => e != null)
        .map((entry) => {
        return de_Result(entry, context);
    });
    return retVal;
};
const de_Transcript = (output, context) => {
    return take(output, {
        Results: (_) => de_ResultList(_, context),
    });
};
const de_TranscriptEvent = (output, context) => {
    return take(output, {
        Transcript: (_) => de_Transcript(_, context),
    });
};
const de_UtteranceEvent = (output, context) => {
    return take(output, {
        BeginOffsetMillis: __expectLong,
        EndOffsetMillis: __expectLong,
        Entities: (_) => de_CallAnalyticsEntityList(_, context),
        IsPartial: __expectBoolean,
        IssuesDetected: _json,
        Items: (_) => de_CallAnalyticsItemList(_, context),
        ParticipantRole: __expectString,
        Sentiment: __expectString,
        Transcript: __expectString,
        UtteranceId: __expectString,
    });
};
const deserializeMetadata = (output) => ({
    httpStatusCode: output.statusCode,
    requestId: output.headers["x-amzn-requestid"] ?? output.headers["x-amzn-request-id"] ?? output.headers["x-amz-request-id"],
    extendedRequestId: output.headers["x-amz-id-2"],
    cfId: output.headers["x-amz-cf-id"],
});
const collectBodyString = (streamBody, context) => collectBody(streamBody, context).then((body) => context.utf8Encoder(body));
const _CIT = "ContentIdentificationType";
const _CRT = "ContentRedactionType";
const _ECI = "EnableChannelIdentification";
const _EPRS = "EnablePartialResultsStabilization";
const _IL = "IdentifyLanguage";
const _IML = "IdentifyMultipleLanguages";
const _LC = "LanguageCode";
const _LMN = "LanguageModelName";
const _LO = "LanguageOptions";
const _ME = "MediaEncoding";
const _MSRH = "MediaSampleRateHertz";
const _NOC = "NumberOfChannels";
const _PET = "PiiEntityTypes";
const _PL = "PreferredLanguage";
const _PRS = "PartialResultsStability";
const _RI = "RequestId";
const _S = "Specialty";
const _SI = "SessionId";
const _SSL = "ShowSpeakerLabel";
const _T = "Type";
const _VFM = "VocabularyFilterMethod";
const _VFN = "VocabularyFilterName";
const _VFNo = "VocabularyFilterNames";
const _VN = "VocabularyName";
const _VNo = "VocabularyNames";
const _xari = "x-amzn-request-id";
const _xatcit = "x-amzn-transcribe-content-identification-type";
const _xatcrt = "x-amzn-transcribe-content-redaction-type";
const _xateci = "x-amzn-transcribe-enable-channel-identification";
const _xateprs = "x-amzn-transcribe-enable-partial-results-stabilization";
const _xatil = "x-amzn-transcribe-identify-language";
const _xatiml = "x-amzn-transcribe-identify-multiple-languages";
const _xatlc = "x-amzn-transcribe-language-code";
const _xatlmn = "x-amzn-transcribe-language-model-name";
const _xatlo = "x-amzn-transcribe-language-options";
const _xatme = "x-amzn-transcribe-media-encoding";
const _xatnoc = "x-amzn-transcribe-number-of-channels";
const _xatpet = "x-amzn-transcribe-pii-entity-types";
const _xatpl = "x-amzn-transcribe-preferred-language";
const _xatprs = "x-amzn-transcribe-partial-results-stability";
const _xats = "x-amzn-transcribe-specialty";
const _xatsi = "x-amzn-transcribe-session-id";
const _xatsr = "x-amzn-transcribe-sample-rate";
const _xatssl = "x-amzn-transcribe-show-speaker-label";
const _xatt = "x-amzn-transcribe-type";
const _xatvfm = "x-amzn-transcribe-vocabulary-filter-method";
const _xatvfn = "x-amzn-transcribe-vocabulary-filter-name";
const _xatvfn_ = "x-amzn-transcribe-vocabulary-filter-names";
const _xatvn = "x-amzn-transcribe-vocabulary-name";
const _xatvn_ = "x-amzn-transcribe-vocabulary-names";
