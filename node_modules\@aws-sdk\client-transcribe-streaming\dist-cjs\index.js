"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __name = (target, value) => __defProp(target, "name", { value, configurable: true });
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);

// src/index.ts
var index_exports = {};
__export(index_exports, {
  AudioStream: () => AudioStream,
  AudioStreamFilterSensitiveLog: () => AudioStreamFilterSensitiveLog,
  BadRequestException: () => BadRequestException,
  CallAnalyticsLanguageCode: () => CallAnalyticsLanguageCode,
  CallAnalyticsTranscriptResultStream: () => CallAnalyticsTranscriptResultStream,
  CallAnalyticsTranscriptResultStreamFilterSensitiveLog: () => CallAnalyticsTranscriptResultStreamFilterSensitiveLog,
  ClinicalNoteGenerationStatus: () => ClinicalNoteGenerationStatus,
  ConflictException: () => ConflictException,
  ContentIdentificationType: () => ContentIdentificationType,
  ContentRedactionOutput: () => ContentRedactionOutput,
  ContentRedactionType: () => ContentRedactionType,
  GetMedicalScribeStreamCommand: () => GetMedicalScribeStreamCommand,
  InternalFailureException: () => InternalFailureException,
  ItemType: () => ItemType,
  LanguageCode: () => LanguageCode,
  LimitExceededException: () => LimitExceededException,
  MediaEncoding: () => MediaEncoding,
  MedicalContentIdentificationType: () => MedicalContentIdentificationType,
  MedicalScribeInputStream: () => MedicalScribeInputStream,
  MedicalScribeInputStreamFilterSensitiveLog: () => MedicalScribeInputStreamFilterSensitiveLog,
  MedicalScribeLanguageCode: () => MedicalScribeLanguageCode,
  MedicalScribeMediaEncoding: () => MedicalScribeMediaEncoding,
  MedicalScribeNoteTemplate: () => MedicalScribeNoteTemplate,
  MedicalScribeParticipantRole: () => MedicalScribeParticipantRole,
  MedicalScribeResultStream: () => MedicalScribeResultStream,
  MedicalScribeResultStreamFilterSensitiveLog: () => MedicalScribeResultStreamFilterSensitiveLog,
  MedicalScribeSessionControlEventType: () => MedicalScribeSessionControlEventType,
  MedicalScribeStreamStatus: () => MedicalScribeStreamStatus,
  MedicalScribeTranscriptItemType: () => MedicalScribeTranscriptItemType,
  MedicalScribeVocabularyFilterMethod: () => MedicalScribeVocabularyFilterMethod,
  MedicalTranscriptResultStream: () => MedicalTranscriptResultStream,
  MedicalTranscriptResultStreamFilterSensitiveLog: () => MedicalTranscriptResultStreamFilterSensitiveLog,
  PartialResultsStability: () => PartialResultsStability,
  ParticipantRole: () => ParticipantRole,
  ResourceNotFoundException: () => ResourceNotFoundException,
  Sentiment: () => Sentiment,
  ServiceUnavailableException: () => ServiceUnavailableException,
  Specialty: () => Specialty,
  StartCallAnalyticsStreamTranscriptionCommand: () => StartCallAnalyticsStreamTranscriptionCommand,
  StartCallAnalyticsStreamTranscriptionRequestFilterSensitiveLog: () => StartCallAnalyticsStreamTranscriptionRequestFilterSensitiveLog,
  StartCallAnalyticsStreamTranscriptionResponseFilterSensitiveLog: () => StartCallAnalyticsStreamTranscriptionResponseFilterSensitiveLog,
  StartMedicalScribeStreamCommand: () => StartMedicalScribeStreamCommand,
  StartMedicalScribeStreamRequestFilterSensitiveLog: () => StartMedicalScribeStreamRequestFilterSensitiveLog,
  StartMedicalScribeStreamResponseFilterSensitiveLog: () => StartMedicalScribeStreamResponseFilterSensitiveLog,
  StartMedicalStreamTranscriptionCommand: () => StartMedicalStreamTranscriptionCommand,
  StartMedicalStreamTranscriptionRequestFilterSensitiveLog: () => StartMedicalStreamTranscriptionRequestFilterSensitiveLog,
  StartMedicalStreamTranscriptionResponseFilterSensitiveLog: () => StartMedicalStreamTranscriptionResponseFilterSensitiveLog,
  StartStreamTranscriptionCommand: () => StartStreamTranscriptionCommand,
  StartStreamTranscriptionRequestFilterSensitiveLog: () => StartStreamTranscriptionRequestFilterSensitiveLog,
  StartStreamTranscriptionResponseFilterSensitiveLog: () => StartStreamTranscriptionResponseFilterSensitiveLog,
  TranscribeStreaming: () => TranscribeStreaming,
  TranscribeStreamingClient: () => TranscribeStreamingClient,
  TranscribeStreamingServiceException: () => TranscribeStreamingServiceException,
  TranscriptResultStream: () => TranscriptResultStream,
  TranscriptResultStreamFilterSensitiveLog: () => TranscriptResultStreamFilterSensitiveLog,
  Type: () => Type,
  VocabularyFilterMethod: () => VocabularyFilterMethod,
  __Client: () => import_smithy_client.Client
});
module.exports = __toCommonJS(index_exports);

// src/TranscribeStreamingClient.ts
var import_middleware_eventstream = require("@aws-sdk/middleware-eventstream");
var import_middleware_host_header = require("@aws-sdk/middleware-host-header");
var import_middleware_logger = require("@aws-sdk/middleware-logger");
var import_middleware_recursion_detection = require("@aws-sdk/middleware-recursion-detection");
var import_middleware_user_agent = require("@aws-sdk/middleware-user-agent");
var import_middleware_websocket = require("@aws-sdk/middleware-websocket");
var import_config_resolver = require("@smithy/config-resolver");
var import_core = require("@smithy/core");
var import_eventstream_serde_config_resolver = require("@smithy/eventstream-serde-config-resolver");
var import_middleware_content_length = require("@smithy/middleware-content-length");
var import_middleware_endpoint = require("@smithy/middleware-endpoint");
var import_middleware_retry = require("@smithy/middleware-retry");

var import_httpAuthSchemeProvider = require("./auth/httpAuthSchemeProvider");

// src/endpoint/EndpointParameters.ts
var resolveClientEndpointParameters = /* @__PURE__ */ __name((options) => {
  return Object.assign(options, {
    useDualstackEndpoint: options.useDualstackEndpoint ?? false,
    useFipsEndpoint: options.useFipsEndpoint ?? false,
    defaultSigningName: "transcribe"
  });
}, "resolveClientEndpointParameters");
var commonParams = {
  UseFIPS: { type: "builtInParams", name: "useFipsEndpoint" },
  Endpoint: { type: "builtInParams", name: "endpoint" },
  Region: { type: "builtInParams", name: "region" },
  UseDualStack: { type: "builtInParams", name: "useDualstackEndpoint" }
};

// src/TranscribeStreamingClient.ts
var import_runtimeConfig = require("././runtimeConfig");

// src/runtimeExtensions.ts
var import_region_config_resolver = require("@aws-sdk/region-config-resolver");
var import_protocol_http = require("@smithy/protocol-http");
var import_smithy_client = require("@smithy/smithy-client");

// src/auth/httpAuthExtensionConfiguration.ts
var getHttpAuthExtensionConfiguration = /* @__PURE__ */ __name((runtimeConfig) => {
  const _httpAuthSchemes = runtimeConfig.httpAuthSchemes;
  let _httpAuthSchemeProvider = runtimeConfig.httpAuthSchemeProvider;
  let _credentials = runtimeConfig.credentials;
  return {
    setHttpAuthScheme(httpAuthScheme) {
      const index = _httpAuthSchemes.findIndex((scheme) => scheme.schemeId === httpAuthScheme.schemeId);
      if (index === -1) {
        _httpAuthSchemes.push(httpAuthScheme);
      } else {
        _httpAuthSchemes.splice(index, 1, httpAuthScheme);
      }
    },
    httpAuthSchemes() {
      return _httpAuthSchemes;
    },
    setHttpAuthSchemeProvider(httpAuthSchemeProvider) {
      _httpAuthSchemeProvider = httpAuthSchemeProvider;
    },
    httpAuthSchemeProvider() {
      return _httpAuthSchemeProvider;
    },
    setCredentials(credentials) {
      _credentials = credentials;
    },
    credentials() {
      return _credentials;
    }
  };
}, "getHttpAuthExtensionConfiguration");
var resolveHttpAuthRuntimeConfig = /* @__PURE__ */ __name((config) => {
  return {
    httpAuthSchemes: config.httpAuthSchemes(),
    httpAuthSchemeProvider: config.httpAuthSchemeProvider(),
    credentials: config.credentials()
  };
}, "resolveHttpAuthRuntimeConfig");

// src/runtimeExtensions.ts
var resolveRuntimeExtensions = /* @__PURE__ */ __name((runtimeConfig, extensions) => {
  const extensionConfiguration = Object.assign(
    (0, import_region_config_resolver.getAwsRegionExtensionConfiguration)(runtimeConfig),
    (0, import_smithy_client.getDefaultExtensionConfiguration)(runtimeConfig),
    (0, import_protocol_http.getHttpHandlerExtensionConfiguration)(runtimeConfig),
    getHttpAuthExtensionConfiguration(runtimeConfig)
  );
  extensions.forEach((extension) => extension.configure(extensionConfiguration));
  return Object.assign(
    runtimeConfig,
    (0, import_region_config_resolver.resolveAwsRegionExtensionConfiguration)(extensionConfiguration),
    (0, import_smithy_client.resolveDefaultRuntimeConfig)(extensionConfiguration),
    (0, import_protocol_http.resolveHttpHandlerRuntimeConfig)(extensionConfiguration),
    resolveHttpAuthRuntimeConfig(extensionConfiguration)
  );
}, "resolveRuntimeExtensions");

// src/TranscribeStreamingClient.ts
var TranscribeStreamingClient = class extends import_smithy_client.Client {
  static {
    __name(this, "TranscribeStreamingClient");
  }
  /**
   * The resolved configuration of TranscribeStreamingClient class. This is resolved and normalized from the {@link TranscribeStreamingClientConfig | constructor configuration interface}.
   */
  config;
  constructor(...[configuration]) {
    const _config_0 = (0, import_runtimeConfig.getRuntimeConfig)(configuration || {});
    super(_config_0);
    this.initConfig = _config_0;
    const _config_1 = resolveClientEndpointParameters(_config_0);
    const _config_2 = (0, import_middleware_user_agent.resolveUserAgentConfig)(_config_1);
    const _config_3 = (0, import_middleware_retry.resolveRetryConfig)(_config_2);
    const _config_4 = (0, import_config_resolver.resolveRegionConfig)(_config_3);
    const _config_5 = (0, import_middleware_host_header.resolveHostHeaderConfig)(_config_4);
    const _config_6 = (0, import_middleware_endpoint.resolveEndpointConfig)(_config_5);
    const _config_7 = (0, import_eventstream_serde_config_resolver.resolveEventStreamSerdeConfig)(_config_6);
    const _config_8 = (0, import_httpAuthSchemeProvider.resolveHttpAuthSchemeConfig)(_config_7);
    const _config_9 = (0, import_middleware_eventstream.resolveEventStreamConfig)(_config_8);
    const _config_10 = (0, import_middleware_websocket.resolveWebSocketConfig)(_config_9);
    const _config_11 = resolveRuntimeExtensions(_config_10, configuration?.extensions || []);
    this.config = _config_11;
    this.middlewareStack.use((0, import_middleware_user_agent.getUserAgentPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_retry.getRetryPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_content_length.getContentLengthPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_host_header.getHostHeaderPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_logger.getLoggerPlugin)(this.config));
    this.middlewareStack.use((0, import_middleware_recursion_detection.getRecursionDetectionPlugin)(this.config));
    this.middlewareStack.use(
      (0, import_core.getHttpAuthSchemeEndpointRuleSetPlugin)(this.config, {
        httpAuthSchemeParametersProvider: import_httpAuthSchemeProvider.defaultTranscribeStreamingHttpAuthSchemeParametersProvider,
        identityProviderConfigProvider: /* @__PURE__ */ __name(async (config) => new import_core.DefaultIdentityProviderConfig({
          "aws.auth#sigv4": config.credentials
        }), "identityProviderConfigProvider")
      })
    );
    this.middlewareStack.use((0, import_core.getHttpSigningPlugin)(this.config));
  }
  /**
   * Destroy underlying resources, like sockets. It's usually not necessary to do this.
   * However in Node.js, it's best to explicitly shut down the client's agent when it is no longer needed.
   * Otherwise, sockets might stay open for quite a long time before the server terminates them.
   */
  destroy() {
    super.destroy();
  }
};

// src/TranscribeStreaming.ts


// src/commands/GetMedicalScribeStreamCommand.ts

var import_middleware_serde = require("@smithy/middleware-serde");


// src/protocols/Aws_restJson1.ts
var import_core2 = require("@aws-sdk/core");



// src/models/TranscribeStreamingServiceException.ts

var TranscribeStreamingServiceException = class _TranscribeStreamingServiceException extends import_smithy_client.ServiceException {
  static {
    __name(this, "TranscribeStreamingServiceException");
  }
  /**
   * @internal
   */
  constructor(options) {
    super(options);
    Object.setPrototypeOf(this, _TranscribeStreamingServiceException.prototype);
  }
};

// src/models/models_0.ts
var ItemType = {
  PRONUNCIATION: "pronunciation",
  PUNCTUATION: "punctuation"
};
var ParticipantRole = {
  AGENT: "AGENT",
  CUSTOMER: "CUSTOMER"
};
var ContentRedactionOutput = {
  REDACTED: "redacted",
  REDACTED_AND_UNREDACTED: "redacted_and_unredacted"
};
var AudioStream;
((AudioStream2) => {
  AudioStream2.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.AudioEvent !== void 0) return visitor.AudioEvent(value.AudioEvent);
    if (value.ConfigurationEvent !== void 0) return visitor.ConfigurationEvent(value.ConfigurationEvent);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(AudioStream || (AudioStream = {}));
var BadRequestException = class _BadRequestException extends TranscribeStreamingServiceException {
  static {
    __name(this, "BadRequestException");
  }
  name = "BadRequestException";
  $fault = "client";
  Message;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "BadRequestException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _BadRequestException.prototype);
    this.Message = opts.Message;
  }
};
var CallAnalyticsLanguageCode = {
  DE_DE: "de-DE",
  EN_AU: "en-AU",
  EN_GB: "en-GB",
  EN_US: "en-US",
  ES_US: "es-US",
  FR_CA: "fr-CA",
  FR_FR: "fr-FR",
  IT_IT: "it-IT",
  PT_BR: "pt-BR"
};
var ConflictException = class _ConflictException extends TranscribeStreamingServiceException {
  static {
    __name(this, "ConflictException");
  }
  name = "ConflictException";
  $fault = "client";
  Message;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ConflictException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ConflictException.prototype);
    this.Message = opts.Message;
  }
};
var InternalFailureException = class _InternalFailureException extends TranscribeStreamingServiceException {
  static {
    __name(this, "InternalFailureException");
  }
  name = "InternalFailureException";
  $fault = "server";
  Message;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "InternalFailureException",
      $fault: "server",
      ...opts
    });
    Object.setPrototypeOf(this, _InternalFailureException.prototype);
    this.Message = opts.Message;
  }
};
var LimitExceededException = class _LimitExceededException extends TranscribeStreamingServiceException {
  static {
    __name(this, "LimitExceededException");
  }
  name = "LimitExceededException";
  $fault = "client";
  Message;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "LimitExceededException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _LimitExceededException.prototype);
    this.Message = opts.Message;
  }
};
var ServiceUnavailableException = class _ServiceUnavailableException extends TranscribeStreamingServiceException {
  static {
    __name(this, "ServiceUnavailableException");
  }
  name = "ServiceUnavailableException";
  $fault = "server";
  Message;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ServiceUnavailableException",
      $fault: "server",
      ...opts
    });
    Object.setPrototypeOf(this, _ServiceUnavailableException.prototype);
    this.Message = opts.Message;
  }
};
var Sentiment = {
  MIXED: "MIXED",
  NEGATIVE: "NEGATIVE",
  NEUTRAL: "NEUTRAL",
  POSITIVE: "POSITIVE"
};
var CallAnalyticsTranscriptResultStream;
((CallAnalyticsTranscriptResultStream3) => {
  CallAnalyticsTranscriptResultStream3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.UtteranceEvent !== void 0) return visitor.UtteranceEvent(value.UtteranceEvent);
    if (value.CategoryEvent !== void 0) return visitor.CategoryEvent(value.CategoryEvent);
    if (value.BadRequestException !== void 0) return visitor.BadRequestException(value.BadRequestException);
    if (value.LimitExceededException !== void 0) return visitor.LimitExceededException(value.LimitExceededException);
    if (value.InternalFailureException !== void 0)
      return visitor.InternalFailureException(value.InternalFailureException);
    if (value.ConflictException !== void 0) return visitor.ConflictException(value.ConflictException);
    if (value.ServiceUnavailableException !== void 0)
      return visitor.ServiceUnavailableException(value.ServiceUnavailableException);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(CallAnalyticsTranscriptResultStream || (CallAnalyticsTranscriptResultStream = {}));
var ClinicalNoteGenerationStatus = {
  COMPLETED: "COMPLETED",
  FAILED: "FAILED",
  IN_PROGRESS: "IN_PROGRESS"
};
var MedicalScribeNoteTemplate = {
  BEHAVIORAL_SOAP: "BEHAVIORAL_SOAP",
  BIRP: "BIRP",
  DAP: "DAP",
  GIRPP: "GIRPP",
  HISTORY_AND_PHYSICAL: "HISTORY_AND_PHYSICAL",
  PHYSICAL_SOAP: "PHYSICAL_SOAP",
  SIRP: "SIRP"
};
var ContentIdentificationType = {
  PII: "PII"
};
var ContentRedactionType = {
  PII: "PII"
};
var MedicalScribeParticipantRole = {
  CLINICIAN: "CLINICIAN",
  PATIENT: "PATIENT"
};
var MedicalScribeLanguageCode = {
  EN_US: "en-US"
};
var MedicalScribeMediaEncoding = {
  FLAC: "flac",
  OGG_OPUS: "ogg-opus",
  PCM: "pcm"
};
var MedicalScribeStreamStatus = {
  COMPLETED: "COMPLETED",
  FAILED: "FAILED",
  IN_PROGRESS: "IN_PROGRESS",
  PAUSED: "PAUSED"
};
var MedicalScribeVocabularyFilterMethod = {
  MASK: "mask",
  REMOVE: "remove",
  TAG: "tag"
};
var ResourceNotFoundException = class _ResourceNotFoundException extends TranscribeStreamingServiceException {
  static {
    __name(this, "ResourceNotFoundException");
  }
  name = "ResourceNotFoundException";
  $fault = "client";
  Message;
  /**
   * @internal
   */
  constructor(opts) {
    super({
      name: "ResourceNotFoundException",
      $fault: "client",
      ...opts
    });
    Object.setPrototypeOf(this, _ResourceNotFoundException.prototype);
    this.Message = opts.Message;
  }
};
var LanguageCode = {
  AF_ZA: "af-ZA",
  AR_AE: "ar-AE",
  AR_SA: "ar-SA",
  CA_ES: "ca-ES",
  CS_CZ: "cs-CZ",
  DA_DK: "da-DK",
  DE_CH: "de-CH",
  DE_DE: "de-DE",
  EL_GR: "el-GR",
  EN_AB: "en-AB",
  EN_AU: "en-AU",
  EN_GB: "en-GB",
  EN_IE: "en-IE",
  EN_IN: "en-IN",
  EN_NZ: "en-NZ",
  EN_US: "en-US",
  EN_WL: "en-WL",
  EN_ZA: "en-ZA",
  ES_ES: "es-ES",
  ES_US: "es-US",
  EU_ES: "eu-ES",
  FA_IR: "fa-IR",
  FI_FI: "fi-FI",
  FR_CA: "fr-CA",
  FR_FR: "fr-FR",
  GL_ES: "gl-ES",
  HE_IL: "he-IL",
  HI_IN: "hi-IN",
  HR_HR: "hr-HR",
  ID_ID: "id-ID",
  IT_IT: "it-IT",
  JA_JP: "ja-JP",
  KO_KR: "ko-KR",
  LV_LV: "lv-LV",
  MS_MY: "ms-MY",
  NL_NL: "nl-NL",
  NO_NO: "no-NO",
  PL_PL: "pl-PL",
  PT_BR: "pt-BR",
  PT_PT: "pt-PT",
  RO_RO: "ro-RO",
  RU_RU: "ru-RU",
  SK_SK: "sk-SK",
  SO_SO: "so-SO",
  SR_RS: "sr-RS",
  SV_SE: "sv-SE",
  TH_TH: "th-TH",
  TL_PH: "tl-PH",
  UK_UA: "uk-UA",
  VI_VN: "vi-VN",
  ZH_CN: "zh-CN",
  ZH_HK: "zh-HK",
  ZH_TW: "zh-TW",
  ZU_ZA: "zu-ZA"
};
var MediaEncoding = {
  FLAC: "flac",
  OGG_OPUS: "ogg-opus",
  PCM: "pcm"
};
var MedicalContentIdentificationType = {
  PHI: "PHI"
};
var MedicalScribeSessionControlEventType = {
  END_OF_SESSION: "END_OF_SESSION"
};
var MedicalScribeInputStream;
((MedicalScribeInputStream2) => {
  MedicalScribeInputStream2.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.AudioEvent !== void 0) return visitor.AudioEvent(value.AudioEvent);
    if (value.SessionControlEvent !== void 0) return visitor.SessionControlEvent(value.SessionControlEvent);
    if (value.ConfigurationEvent !== void 0) return visitor.ConfigurationEvent(value.ConfigurationEvent);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(MedicalScribeInputStream || (MedicalScribeInputStream = {}));
var MedicalScribeTranscriptItemType = {
  PRONUNCIATION: "pronunciation",
  PUNCTUATION: "punctuation"
};
var MedicalScribeResultStream;
((MedicalScribeResultStream3) => {
  MedicalScribeResultStream3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.TranscriptEvent !== void 0) return visitor.TranscriptEvent(value.TranscriptEvent);
    if (value.BadRequestException !== void 0) return visitor.BadRequestException(value.BadRequestException);
    if (value.LimitExceededException !== void 0) return visitor.LimitExceededException(value.LimitExceededException);
    if (value.InternalFailureException !== void 0)
      return visitor.InternalFailureException(value.InternalFailureException);
    if (value.ConflictException !== void 0) return visitor.ConflictException(value.ConflictException);
    if (value.ServiceUnavailableException !== void 0)
      return visitor.ServiceUnavailableException(value.ServiceUnavailableException);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(MedicalScribeResultStream || (MedicalScribeResultStream = {}));
var MedicalTranscriptResultStream;
((MedicalTranscriptResultStream3) => {
  MedicalTranscriptResultStream3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.TranscriptEvent !== void 0) return visitor.TranscriptEvent(value.TranscriptEvent);
    if (value.BadRequestException !== void 0) return visitor.BadRequestException(value.BadRequestException);
    if (value.LimitExceededException !== void 0) return visitor.LimitExceededException(value.LimitExceededException);
    if (value.InternalFailureException !== void 0)
      return visitor.InternalFailureException(value.InternalFailureException);
    if (value.ConflictException !== void 0) return visitor.ConflictException(value.ConflictException);
    if (value.ServiceUnavailableException !== void 0)
      return visitor.ServiceUnavailableException(value.ServiceUnavailableException);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(MedicalTranscriptResultStream || (MedicalTranscriptResultStream = {}));
var PartialResultsStability = {
  HIGH: "high",
  LOW: "low",
  MEDIUM: "medium"
};
var Specialty = {
  CARDIOLOGY: "CARDIOLOGY",
  NEUROLOGY: "NEUROLOGY",
  ONCOLOGY: "ONCOLOGY",
  PRIMARYCARE: "PRIMARYCARE",
  RADIOLOGY: "RADIOLOGY",
  UROLOGY: "UROLOGY"
};
var VocabularyFilterMethod = {
  MASK: "mask",
  REMOVE: "remove",
  TAG: "tag"
};
var Type = {
  CONVERSATION: "CONVERSATION",
  DICTATION: "DICTATION"
};
var TranscriptResultStream;
((TranscriptResultStream3) => {
  TranscriptResultStream3.visit = /* @__PURE__ */ __name((value, visitor) => {
    if (value.TranscriptEvent !== void 0) return visitor.TranscriptEvent(value.TranscriptEvent);
    if (value.BadRequestException !== void 0) return visitor.BadRequestException(value.BadRequestException);
    if (value.LimitExceededException !== void 0) return visitor.LimitExceededException(value.LimitExceededException);
    if (value.InternalFailureException !== void 0)
      return visitor.InternalFailureException(value.InternalFailureException);
    if (value.ConflictException !== void 0) return visitor.ConflictException(value.ConflictException);
    if (value.ServiceUnavailableException !== void 0)
      return visitor.ServiceUnavailableException(value.ServiceUnavailableException);
    return visitor._(value.$unknown[0], value.$unknown[1]);
  }, "visit");
})(TranscriptResultStream || (TranscriptResultStream = {}));
var AudioStreamFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.AudioEvent !== void 0) return { AudioEvent: obj.AudioEvent };
  if (obj.ConfigurationEvent !== void 0) return { ConfigurationEvent: obj.ConfigurationEvent };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "AudioStreamFilterSensitiveLog");
var CallAnalyticsTranscriptResultStreamFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.UtteranceEvent !== void 0) return { UtteranceEvent: obj.UtteranceEvent };
  if (obj.CategoryEvent !== void 0) return { CategoryEvent: obj.CategoryEvent };
  if (obj.BadRequestException !== void 0) return { BadRequestException: obj.BadRequestException };
  if (obj.LimitExceededException !== void 0) return { LimitExceededException: obj.LimitExceededException };
  if (obj.InternalFailureException !== void 0) return { InternalFailureException: obj.InternalFailureException };
  if (obj.ConflictException !== void 0) return { ConflictException: obj.ConflictException };
  if (obj.ServiceUnavailableException !== void 0)
    return { ServiceUnavailableException: obj.ServiceUnavailableException };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "CallAnalyticsTranscriptResultStreamFilterSensitiveLog");
var MedicalScribeInputStreamFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.AudioEvent !== void 0) return { AudioEvent: obj.AudioEvent };
  if (obj.SessionControlEvent !== void 0) return { SessionControlEvent: obj.SessionControlEvent };
  if (obj.ConfigurationEvent !== void 0) return { ConfigurationEvent: obj.ConfigurationEvent };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "MedicalScribeInputStreamFilterSensitiveLog");
var MedicalScribeResultStreamFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.TranscriptEvent !== void 0) return { TranscriptEvent: obj.TranscriptEvent };
  if (obj.BadRequestException !== void 0) return { BadRequestException: obj.BadRequestException };
  if (obj.LimitExceededException !== void 0) return { LimitExceededException: obj.LimitExceededException };
  if (obj.InternalFailureException !== void 0) return { InternalFailureException: obj.InternalFailureException };
  if (obj.ConflictException !== void 0) return { ConflictException: obj.ConflictException };
  if (obj.ServiceUnavailableException !== void 0)
    return { ServiceUnavailableException: obj.ServiceUnavailableException };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "MedicalScribeResultStreamFilterSensitiveLog");
var MedicalTranscriptResultStreamFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.TranscriptEvent !== void 0) return { TranscriptEvent: obj.TranscriptEvent };
  if (obj.BadRequestException !== void 0) return { BadRequestException: obj.BadRequestException };
  if (obj.LimitExceededException !== void 0) return { LimitExceededException: obj.LimitExceededException };
  if (obj.InternalFailureException !== void 0) return { InternalFailureException: obj.InternalFailureException };
  if (obj.ConflictException !== void 0) return { ConflictException: obj.ConflictException };
  if (obj.ServiceUnavailableException !== void 0)
    return { ServiceUnavailableException: obj.ServiceUnavailableException };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "MedicalTranscriptResultStreamFilterSensitiveLog");
var StartCallAnalyticsStreamTranscriptionRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AudioStream && { AudioStream: "STREAMING_CONTENT" }
}), "StartCallAnalyticsStreamTranscriptionRequestFilterSensitiveLog");
var StartCallAnalyticsStreamTranscriptionResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.CallAnalyticsTranscriptResultStream && { CallAnalyticsTranscriptResultStream: "STREAMING_CONTENT" }
}), "StartCallAnalyticsStreamTranscriptionResponseFilterSensitiveLog");
var StartMedicalScribeStreamRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.InputStream && { InputStream: "STREAMING_CONTENT" }
}), "StartMedicalScribeStreamRequestFilterSensitiveLog");
var StartMedicalScribeStreamResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.ResultStream && { ResultStream: "STREAMING_CONTENT" }
}), "StartMedicalScribeStreamResponseFilterSensitiveLog");
var StartMedicalStreamTranscriptionRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AudioStream && { AudioStream: "STREAMING_CONTENT" }
}), "StartMedicalStreamTranscriptionRequestFilterSensitiveLog");
var StartMedicalStreamTranscriptionResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.TranscriptResultStream && { TranscriptResultStream: "STREAMING_CONTENT" }
}), "StartMedicalStreamTranscriptionResponseFilterSensitiveLog");
var StartStreamTranscriptionRequestFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.AudioStream && { AudioStream: "STREAMING_CONTENT" }
}), "StartStreamTranscriptionRequestFilterSensitiveLog");
var TranscriptResultStreamFilterSensitiveLog = /* @__PURE__ */ __name((obj) => {
  if (obj.TranscriptEvent !== void 0) return { TranscriptEvent: obj.TranscriptEvent };
  if (obj.BadRequestException !== void 0) return { BadRequestException: obj.BadRequestException };
  if (obj.LimitExceededException !== void 0) return { LimitExceededException: obj.LimitExceededException };
  if (obj.InternalFailureException !== void 0) return { InternalFailureException: obj.InternalFailureException };
  if (obj.ConflictException !== void 0) return { ConflictException: obj.ConflictException };
  if (obj.ServiceUnavailableException !== void 0)
    return { ServiceUnavailableException: obj.ServiceUnavailableException };
  if (obj.$unknown !== void 0) return { [obj.$unknown[0]]: "UNKNOWN" };
}, "TranscriptResultStreamFilterSensitiveLog");
var StartStreamTranscriptionResponseFilterSensitiveLog = /* @__PURE__ */ __name((obj) => ({
  ...obj,
  ...obj.TranscriptResultStream && { TranscriptResultStream: "STREAMING_CONTENT" }
}), "StartStreamTranscriptionResponseFilterSensitiveLog");

// src/protocols/Aws_restJson1.ts
var se_GetMedicalScribeStreamCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = {};
  b.bp("/medical-scribe-stream/{SessionId}");
  b.p("SessionId", () => input.SessionId, "{SessionId}", false);
  let body;
  b.m("GET").h(headers).b(body);
  return b.build();
}, "se_GetMedicalScribeStreamCommand");
var se_StartCallAnalyticsStreamTranscriptionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/json",
    [_xatlc]: input[_LC],
    [_xatsr]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_MSRH]), () => input[_MSRH].toString()],
    [_xatme]: input[_ME],
    [_xatvn]: input[_VN],
    [_xatsi]: input[_SI],
    [_xatvfn]: input[_VFN],
    [_xatvfm]: input[_VFM],
    [_xatlmn]: input[_LMN],
    [_xateprs]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_EPRS]), () => input[_EPRS].toString()],
    [_xatprs]: input[_PRS],
    [_xatcit]: input[_CIT],
    [_xatcrt]: input[_CRT],
    [_xatpet]: input[_PET]
  });
  b.bp("/call-analytics-stream-transcription");
  let body;
  if (input.AudioStream !== void 0) {
    body = se_AudioStream(input.AudioStream, context);
  }
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_StartCallAnalyticsStreamTranscriptionCommand");
var se_StartMedicalScribeStreamCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/json",
    [_xatsi]: input[_SI],
    [_xatlc]: input[_LC],
    [_xatsr]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_MSRH]), () => input[_MSRH].toString()],
    [_xatme]: input[_ME]
  });
  b.bp("/medical-scribe-stream");
  let body;
  if (input.InputStream !== void 0) {
    body = se_MedicalScribeInputStream(input.InputStream, context);
  }
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_StartMedicalScribeStreamCommand");
var se_StartMedicalStreamTranscriptionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/json",
    [_xatlc]: input[_LC],
    [_xatsr]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_MSRH]), () => input[_MSRH].toString()],
    [_xatme]: input[_ME],
    [_xatvn]: input[_VN],
    [_xats]: input[_S],
    [_xatt]: input[_T],
    [_xatssl]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_SSL]), () => input[_SSL].toString()],
    [_xatsi]: input[_SI],
    [_xateci]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_ECI]), () => input[_ECI].toString()],
    [_xatnoc]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_NOC]), () => input[_NOC].toString()],
    [_xatcit]: input[_CIT]
  });
  b.bp("/medical-stream-transcription");
  let body;
  if (input.AudioStream !== void 0) {
    body = se_AudioStream(input.AudioStream, context);
  }
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_StartMedicalStreamTranscriptionCommand");
var se_StartStreamTranscriptionCommand = /* @__PURE__ */ __name(async (input, context) => {
  const b = (0, import_core.requestBuilder)(input, context);
  const headers = (0, import_smithy_client.map)({}, import_smithy_client.isSerializableHeaderValue, {
    "content-type": "application/json",
    [_xatlc]: input[_LC],
    [_xatsr]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_MSRH]), () => input[_MSRH].toString()],
    [_xatme]: input[_ME],
    [_xatvn]: input[_VN],
    [_xatsi]: input[_SI],
    [_xatvfn]: input[_VFN],
    [_xatvfm]: input[_VFM],
    [_xatssl]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_SSL]), () => input[_SSL].toString()],
    [_xateci]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_ECI]), () => input[_ECI].toString()],
    [_xatnoc]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_NOC]), () => input[_NOC].toString()],
    [_xateprs]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_EPRS]), () => input[_EPRS].toString()],
    [_xatprs]: input[_PRS],
    [_xatcit]: input[_CIT],
    [_xatcrt]: input[_CRT],
    [_xatpet]: input[_PET],
    [_xatlmn]: input[_LMN],
    [_xatil]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_IL]), () => input[_IL].toString()],
    [_xatlo]: input[_LO],
    [_xatpl]: input[_PL],
    [_xatiml]: [() => (0, import_smithy_client.isSerializableHeaderValue)(input[_IML]), () => input[_IML].toString()],
    [_xatvn_]: input[_VNo],
    [_xatvfn_]: input[_VFNo]
  });
  b.bp("/stream-transcription");
  let body;
  if (input.AudioStream !== void 0) {
    body = se_AudioStream(input.AudioStream, context);
  }
  b.m("POST").h(headers).b(body);
  return b.build();
}, "se_StartStreamTranscriptionCommand");
var de_GetMedicalScribeStreamCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output)
  });
  const data = (0, import_smithy_client.expectNonNull)((0, import_smithy_client.expectObject)(await (0, import_core2.parseJsonBody)(output.body, context)), "body");
  const doc = (0, import_smithy_client.take)(data, {
    MedicalScribeStreamDetails: /* @__PURE__ */ __name((_) => de_MedicalScribeStreamDetails(_, context), "MedicalScribeStreamDetails")
  });
  Object.assign(contents, doc);
  return contents;
}, "de_GetMedicalScribeStreamCommand");
var de_StartCallAnalyticsStreamTranscriptionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RI]: [, output.headers[_xari]],
    [_LC]: [, output.headers[_xatlc]],
    [_MSRH]: [() => void 0 !== output.headers[_xatsr], () => (0, import_smithy_client.strictParseInt32)(output.headers[_xatsr])],
    [_ME]: [, output.headers[_xatme]],
    [_VN]: [, output.headers[_xatvn]],
    [_SI]: [, output.headers[_xatsi]],
    [_VFN]: [, output.headers[_xatvfn]],
    [_VFM]: [, output.headers[_xatvfm]],
    [_LMN]: [, output.headers[_xatlmn]],
    [_EPRS]: [() => void 0 !== output.headers[_xateprs], () => (0, import_smithy_client.parseBoolean)(output.headers[_xateprs])],
    [_PRS]: [, output.headers[_xatprs]],
    [_CIT]: [, output.headers[_xatcit]],
    [_CRT]: [, output.headers[_xatcrt]],
    [_PET]: [, output.headers[_xatpet]]
  });
  const data = output.body;
  contents.CallAnalyticsTranscriptResultStream = de_CallAnalyticsTranscriptResultStream(data, context);
  return contents;
}, "de_StartCallAnalyticsStreamTranscriptionCommand");
var de_StartMedicalScribeStreamCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_SI]: [, output.headers[_xatsi]],
    [_RI]: [, output.headers[_xari]],
    [_LC]: [, output.headers[_xatlc]],
    [_MSRH]: [() => void 0 !== output.headers[_xatsr], () => (0, import_smithy_client.strictParseInt32)(output.headers[_xatsr])],
    [_ME]: [, output.headers[_xatme]]
  });
  const data = output.body;
  contents.ResultStream = de_MedicalScribeResultStream(data, context);
  return contents;
}, "de_StartMedicalScribeStreamCommand");
var de_StartMedicalStreamTranscriptionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RI]: [, output.headers[_xari]],
    [_LC]: [, output.headers[_xatlc]],
    [_MSRH]: [() => void 0 !== output.headers[_xatsr], () => (0, import_smithy_client.strictParseInt32)(output.headers[_xatsr])],
    [_ME]: [, output.headers[_xatme]],
    [_VN]: [, output.headers[_xatvn]],
    [_S]: [, output.headers[_xats]],
    [_T]: [, output.headers[_xatt]],
    [_SSL]: [() => void 0 !== output.headers[_xatssl], () => (0, import_smithy_client.parseBoolean)(output.headers[_xatssl])],
    [_SI]: [, output.headers[_xatsi]],
    [_ECI]: [() => void 0 !== output.headers[_xateci], () => (0, import_smithy_client.parseBoolean)(output.headers[_xateci])],
    [_NOC]: [() => void 0 !== output.headers[_xatnoc], () => (0, import_smithy_client.strictParseInt32)(output.headers[_xatnoc])],
    [_CIT]: [, output.headers[_xatcit]]
  });
  const data = output.body;
  contents.TranscriptResultStream = de_MedicalTranscriptResultStream(data, context);
  return contents;
}, "de_StartMedicalStreamTranscriptionCommand");
var de_StartStreamTranscriptionCommand = /* @__PURE__ */ __name(async (output, context) => {
  if (output.statusCode !== 200 && output.statusCode >= 300) {
    return de_CommandError(output, context);
  }
  const contents = (0, import_smithy_client.map)({
    $metadata: deserializeMetadata(output),
    [_RI]: [, output.headers[_xari]],
    [_LC]: [, output.headers[_xatlc]],
    [_MSRH]: [() => void 0 !== output.headers[_xatsr], () => (0, import_smithy_client.strictParseInt32)(output.headers[_xatsr])],
    [_ME]: [, output.headers[_xatme]],
    [_VN]: [, output.headers[_xatvn]],
    [_SI]: [, output.headers[_xatsi]],
    [_VFN]: [, output.headers[_xatvfn]],
    [_VFM]: [, output.headers[_xatvfm]],
    [_SSL]: [() => void 0 !== output.headers[_xatssl], () => (0, import_smithy_client.parseBoolean)(output.headers[_xatssl])],
    [_ECI]: [() => void 0 !== output.headers[_xateci], () => (0, import_smithy_client.parseBoolean)(output.headers[_xateci])],
    [_NOC]: [() => void 0 !== output.headers[_xatnoc], () => (0, import_smithy_client.strictParseInt32)(output.headers[_xatnoc])],
    [_EPRS]: [() => void 0 !== output.headers[_xateprs], () => (0, import_smithy_client.parseBoolean)(output.headers[_xateprs])],
    [_PRS]: [, output.headers[_xatprs]],
    [_CIT]: [, output.headers[_xatcit]],
    [_CRT]: [, output.headers[_xatcrt]],
    [_PET]: [, output.headers[_xatpet]],
    [_LMN]: [, output.headers[_xatlmn]],
    [_IL]: [() => void 0 !== output.headers[_xatil], () => (0, import_smithy_client.parseBoolean)(output.headers[_xatil])],
    [_LO]: [, output.headers[_xatlo]],
    [_PL]: [, output.headers[_xatpl]],
    [_IML]: [() => void 0 !== output.headers[_xatiml], () => (0, import_smithy_client.parseBoolean)(output.headers[_xatiml])],
    [_VNo]: [, output.headers[_xatvn_]],
    [_VFNo]: [, output.headers[_xatvfn_]]
  });
  const data = output.body;
  contents.TranscriptResultStream = de_TranscriptResultStream(data, context);
  return contents;
}, "de_StartStreamTranscriptionCommand");
var de_CommandError = /* @__PURE__ */ __name(async (output, context) => {
  const parsedOutput = {
    ...output,
    body: await (0, import_core2.parseJsonErrorBody)(output.body, context)
  };
  const errorCode = (0, import_core2.loadRestJsonErrorCode)(output, parsedOutput.body);
  switch (errorCode) {
    case "BadRequestException":
    case "com.amazonaws.transcribestreaming#BadRequestException":
      throw await de_BadRequestExceptionRes(parsedOutput, context);
    case "InternalFailureException":
    case "com.amazonaws.transcribestreaming#InternalFailureException":
      throw await de_InternalFailureExceptionRes(parsedOutput, context);
    case "LimitExceededException":
    case "com.amazonaws.transcribestreaming#LimitExceededException":
      throw await de_LimitExceededExceptionRes(parsedOutput, context);
    case "ResourceNotFoundException":
    case "com.amazonaws.transcribestreaming#ResourceNotFoundException":
      throw await de_ResourceNotFoundExceptionRes(parsedOutput, context);
    case "ConflictException":
    case "com.amazonaws.transcribestreaming#ConflictException":
      throw await de_ConflictExceptionRes(parsedOutput, context);
    case "ServiceUnavailableException":
    case "com.amazonaws.transcribestreaming#ServiceUnavailableException":
      throw await de_ServiceUnavailableExceptionRes(parsedOutput, context);
    default:
      const parsedBody = parsedOutput.body;
      return throwDefaultError({
        output,
        parsedBody,
        errorCode
      });
  }
}, "de_CommandError");
var throwDefaultError = (0, import_smithy_client.withBaseException)(TranscribeStreamingServiceException);
var de_BadRequestExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    Message: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new BadRequestException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_BadRequestExceptionRes");
var de_ConflictExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    Message: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new ConflictException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_ConflictExceptionRes");
var de_InternalFailureExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    Message: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new InternalFailureException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_InternalFailureExceptionRes");
var de_LimitExceededExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    Message: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new LimitExceededException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_LimitExceededExceptionRes");
var de_ResourceNotFoundExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    Message: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new ResourceNotFoundException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_ResourceNotFoundExceptionRes");
var de_ServiceUnavailableExceptionRes = /* @__PURE__ */ __name(async (parsedOutput, context) => {
  const contents = (0, import_smithy_client.map)({});
  const data = parsedOutput.body;
  const doc = (0, import_smithy_client.take)(data, {
    Message: import_smithy_client.expectString
  });
  Object.assign(contents, doc);
  const exception = new ServiceUnavailableException({
    $metadata: deserializeMetadata(parsedOutput),
    ...contents
  });
  return (0, import_smithy_client.decorateServiceException)(exception, parsedOutput.body);
}, "de_ServiceUnavailableExceptionRes");
var se_AudioStream = /* @__PURE__ */ __name((input, context) => {
  const eventMarshallingVisitor = /* @__PURE__ */ __name((event) => AudioStream.visit(event, {
    AudioEvent: /* @__PURE__ */ __name((value) => se_AudioEvent_event(value, context), "AudioEvent"),
    ConfigurationEvent: /* @__PURE__ */ __name((value) => se_ConfigurationEvent_event(value, context), "ConfigurationEvent"),
    _: /* @__PURE__ */ __name((value) => value, "_")
  }), "eventMarshallingVisitor");
  return context.eventStreamMarshaller.serialize(input, eventMarshallingVisitor);
}, "se_AudioStream");
var se_MedicalScribeInputStream = /* @__PURE__ */ __name((input, context) => {
  const eventMarshallingVisitor = /* @__PURE__ */ __name((event) => MedicalScribeInputStream.visit(event, {
    AudioEvent: /* @__PURE__ */ __name((value) => se_MedicalScribeAudioEvent_event(value, context), "AudioEvent"),
    SessionControlEvent: /* @__PURE__ */ __name((value) => se_MedicalScribeSessionControlEvent_event(value, context), "SessionControlEvent"),
    ConfigurationEvent: /* @__PURE__ */ __name((value) => se_MedicalScribeConfigurationEvent_event(value, context), "ConfigurationEvent"),
    _: /* @__PURE__ */ __name((value) => value, "_")
  }), "eventMarshallingVisitor");
  return context.eventStreamMarshaller.serialize(input, eventMarshallingVisitor);
}, "se_MedicalScribeInputStream");
var se_AudioEvent_event = /* @__PURE__ */ __name((input, context) => {
  const headers = {
    ":event-type": { type: "string", value: "AudioEvent" },
    ":message-type": { type: "string", value: "event" },
    ":content-type": { type: "string", value: "application/octet-stream" }
  };
  let body = new Uint8Array();
  if (input.AudioChunk != null) {
    body = input.AudioChunk;
  }
  return { headers, body };
}, "se_AudioEvent_event");
var se_ConfigurationEvent_event = /* @__PURE__ */ __name((input, context) => {
  const headers = {
    ":event-type": { type: "string", value: "ConfigurationEvent" },
    ":message-type": { type: "string", value: "event" },
    ":content-type": { type: "string", value: "application/json" }
  };
  let body = new Uint8Array();
  body = (0, import_smithy_client._json)(input);
  body = context.utf8Decoder(JSON.stringify(body));
  return { headers, body };
}, "se_ConfigurationEvent_event");
var se_MedicalScribeAudioEvent_event = /* @__PURE__ */ __name((input, context) => {
  const headers = {
    ":event-type": { type: "string", value: "AudioEvent" },
    ":message-type": { type: "string", value: "event" },
    ":content-type": { type: "string", value: "application/octet-stream" }
  };
  let body = new Uint8Array();
  if (input.AudioChunk != null) {
    body = input.AudioChunk;
  }
  return { headers, body };
}, "se_MedicalScribeAudioEvent_event");
var se_MedicalScribeConfigurationEvent_event = /* @__PURE__ */ __name((input, context) => {
  const headers = {
    ":event-type": { type: "string", value: "ConfigurationEvent" },
    ":message-type": { type: "string", value: "event" },
    ":content-type": { type: "string", value: "application/json" }
  };
  let body = new Uint8Array();
  body = (0, import_smithy_client._json)(input);
  body = context.utf8Decoder(JSON.stringify(body));
  return { headers, body };
}, "se_MedicalScribeConfigurationEvent_event");
var se_MedicalScribeSessionControlEvent_event = /* @__PURE__ */ __name((input, context) => {
  const headers = {
    ":event-type": { type: "string", value: "SessionControlEvent" },
    ":message-type": { type: "string", value: "event" },
    ":content-type": { type: "string", value: "application/json" }
  };
  let body = new Uint8Array();
  body = (0, import_smithy_client._json)(input);
  body = context.utf8Decoder(JSON.stringify(body));
  return { headers, body };
}, "se_MedicalScribeSessionControlEvent_event");
var de_CallAnalyticsTranscriptResultStream = /* @__PURE__ */ __name((output, context) => {
  return context.eventStreamMarshaller.deserialize(output, async (event) => {
    if (event["UtteranceEvent"] != null) {
      return {
        UtteranceEvent: await de_UtteranceEvent_event(event["UtteranceEvent"], context)
      };
    }
    if (event["CategoryEvent"] != null) {
      return {
        CategoryEvent: await de_CategoryEvent_event(event["CategoryEvent"], context)
      };
    }
    if (event["BadRequestException"] != null) {
      return {
        BadRequestException: await de_BadRequestException_event(event["BadRequestException"], context)
      };
    }
    if (event["LimitExceededException"] != null) {
      return {
        LimitExceededException: await de_LimitExceededException_event(event["LimitExceededException"], context)
      };
    }
    if (event["InternalFailureException"] != null) {
      return {
        InternalFailureException: await de_InternalFailureException_event(event["InternalFailureException"], context)
      };
    }
    if (event["ConflictException"] != null) {
      return {
        ConflictException: await de_ConflictException_event(event["ConflictException"], context)
      };
    }
    if (event["ServiceUnavailableException"] != null) {
      return {
        ServiceUnavailableException: await de_ServiceUnavailableException_event(
          event["ServiceUnavailableException"],
          context
        )
      };
    }
    return { $unknown: event };
  });
}, "de_CallAnalyticsTranscriptResultStream");
var de_MedicalScribeResultStream = /* @__PURE__ */ __name((output, context) => {
  return context.eventStreamMarshaller.deserialize(output, async (event) => {
    if (event["TranscriptEvent"] != null) {
      return {
        TranscriptEvent: await de_MedicalScribeTranscriptEvent_event(event["TranscriptEvent"], context)
      };
    }
    if (event["BadRequestException"] != null) {
      return {
        BadRequestException: await de_BadRequestException_event(event["BadRequestException"], context)
      };
    }
    if (event["LimitExceededException"] != null) {
      return {
        LimitExceededException: await de_LimitExceededException_event(event["LimitExceededException"], context)
      };
    }
    if (event["InternalFailureException"] != null) {
      return {
        InternalFailureException: await de_InternalFailureException_event(event["InternalFailureException"], context)
      };
    }
    if (event["ConflictException"] != null) {
      return {
        ConflictException: await de_ConflictException_event(event["ConflictException"], context)
      };
    }
    if (event["ServiceUnavailableException"] != null) {
      return {
        ServiceUnavailableException: await de_ServiceUnavailableException_event(
          event["ServiceUnavailableException"],
          context
        )
      };
    }
    return { $unknown: event };
  });
}, "de_MedicalScribeResultStream");
var de_MedicalTranscriptResultStream = /* @__PURE__ */ __name((output, context) => {
  return context.eventStreamMarshaller.deserialize(output, async (event) => {
    if (event["TranscriptEvent"] != null) {
      return {
        TranscriptEvent: await de_MedicalTranscriptEvent_event(event["TranscriptEvent"], context)
      };
    }
    if (event["BadRequestException"] != null) {
      return {
        BadRequestException: await de_BadRequestException_event(event["BadRequestException"], context)
      };
    }
    if (event["LimitExceededException"] != null) {
      return {
        LimitExceededException: await de_LimitExceededException_event(event["LimitExceededException"], context)
      };
    }
    if (event["InternalFailureException"] != null) {
      return {
        InternalFailureException: await de_InternalFailureException_event(event["InternalFailureException"], context)
      };
    }
    if (event["ConflictException"] != null) {
      return {
        ConflictException: await de_ConflictException_event(event["ConflictException"], context)
      };
    }
    if (event["ServiceUnavailableException"] != null) {
      return {
        ServiceUnavailableException: await de_ServiceUnavailableException_event(
          event["ServiceUnavailableException"],
          context
        )
      };
    }
    return { $unknown: event };
  });
}, "de_MedicalTranscriptResultStream");
var de_TranscriptResultStream = /* @__PURE__ */ __name((output, context) => {
  return context.eventStreamMarshaller.deserialize(output, async (event) => {
    if (event["TranscriptEvent"] != null) {
      return {
        TranscriptEvent: await de_TranscriptEvent_event(event["TranscriptEvent"], context)
      };
    }
    if (event["BadRequestException"] != null) {
      return {
        BadRequestException: await de_BadRequestException_event(event["BadRequestException"], context)
      };
    }
    if (event["LimitExceededException"] != null) {
      return {
        LimitExceededException: await de_LimitExceededException_event(event["LimitExceededException"], context)
      };
    }
    if (event["InternalFailureException"] != null) {
      return {
        InternalFailureException: await de_InternalFailureException_event(event["InternalFailureException"], context)
      };
    }
    if (event["ConflictException"] != null) {
      return {
        ConflictException: await de_ConflictException_event(event["ConflictException"], context)
      };
    }
    if (event["ServiceUnavailableException"] != null) {
      return {
        ServiceUnavailableException: await de_ServiceUnavailableException_event(
          event["ServiceUnavailableException"],
          context
        )
      };
    }
    return { $unknown: event };
  });
}, "de_TranscriptResultStream");
var de_BadRequestException_event = /* @__PURE__ */ __name(async (output, context) => {
  const parsedOutput = {
    ...output,
    body: await (0, import_core2.parseJsonBody)(output.body, context)
  };
  return de_BadRequestExceptionRes(parsedOutput, context);
}, "de_BadRequestException_event");
var de_CategoryEvent_event = /* @__PURE__ */ __name(async (output, context) => {
  const contents = {};
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  Object.assign(contents, (0, import_smithy_client._json)(data));
  return contents;
}, "de_CategoryEvent_event");
var de_ConflictException_event = /* @__PURE__ */ __name(async (output, context) => {
  const parsedOutput = {
    ...output,
    body: await (0, import_core2.parseJsonBody)(output.body, context)
  };
  return de_ConflictExceptionRes(parsedOutput, context);
}, "de_ConflictException_event");
var de_InternalFailureException_event = /* @__PURE__ */ __name(async (output, context) => {
  const parsedOutput = {
    ...output,
    body: await (0, import_core2.parseJsonBody)(output.body, context)
  };
  return de_InternalFailureExceptionRes(parsedOutput, context);
}, "de_InternalFailureException_event");
var de_LimitExceededException_event = /* @__PURE__ */ __name(async (output, context) => {
  const parsedOutput = {
    ...output,
    body: await (0, import_core2.parseJsonBody)(output.body, context)
  };
  return de_LimitExceededExceptionRes(parsedOutput, context);
}, "de_LimitExceededException_event");
var de_MedicalScribeTranscriptEvent_event = /* @__PURE__ */ __name(async (output, context) => {
  const contents = {};
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  Object.assign(contents, de_MedicalScribeTranscriptEvent(data, context));
  return contents;
}, "de_MedicalScribeTranscriptEvent_event");
var de_MedicalTranscriptEvent_event = /* @__PURE__ */ __name(async (output, context) => {
  const contents = {};
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  Object.assign(contents, de_MedicalTranscriptEvent(data, context));
  return contents;
}, "de_MedicalTranscriptEvent_event");
var de_ServiceUnavailableException_event = /* @__PURE__ */ __name(async (output, context) => {
  const parsedOutput = {
    ...output,
    body: await (0, import_core2.parseJsonBody)(output.body, context)
  };
  return de_ServiceUnavailableExceptionRes(parsedOutput, context);
}, "de_ServiceUnavailableException_event");
var de_TranscriptEvent_event = /* @__PURE__ */ __name(async (output, context) => {
  const contents = {};
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  Object.assign(contents, de_TranscriptEvent(data, context));
  return contents;
}, "de_TranscriptEvent_event");
var de_UtteranceEvent_event = /* @__PURE__ */ __name(async (output, context) => {
  const contents = {};
  const data = await (0, import_core2.parseJsonBody)(output.body, context);
  Object.assign(contents, de_UtteranceEvent(data, context));
  return contents;
}, "de_UtteranceEvent_event");
var de_Alternative = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Entities: /* @__PURE__ */ __name((_) => de_EntityList(_, context), "Entities"),
    Items: /* @__PURE__ */ __name((_) => de_ItemList(_, context), "Items"),
    Transcript: import_smithy_client.expectString
  });
}, "de_Alternative");
var de_AlternativeList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_Alternative(entry, context);
  });
  return retVal;
}, "de_AlternativeList");
var de_CallAnalyticsEntity = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    BeginOffsetMillis: import_smithy_client.expectLong,
    Category: import_smithy_client.expectString,
    Confidence: import_smithy_client.limitedParseDouble,
    Content: import_smithy_client.expectString,
    EndOffsetMillis: import_smithy_client.expectLong,
    Type: import_smithy_client.expectString
  });
}, "de_CallAnalyticsEntity");
var de_CallAnalyticsEntityList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_CallAnalyticsEntity(entry, context);
  });
  return retVal;
}, "de_CallAnalyticsEntityList");
var de_CallAnalyticsItem = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    BeginOffsetMillis: import_smithy_client.expectLong,
    Confidence: import_smithy_client.limitedParseDouble,
    Content: import_smithy_client.expectString,
    EndOffsetMillis: import_smithy_client.expectLong,
    Stable: import_smithy_client.expectBoolean,
    Type: import_smithy_client.expectString,
    VocabularyFilterMatch: import_smithy_client.expectBoolean
  });
}, "de_CallAnalyticsItem");
var de_CallAnalyticsItemList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_CallAnalyticsItem(entry, context);
  });
  return retVal;
}, "de_CallAnalyticsItemList");
var de_Entity = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Category: import_smithy_client.expectString,
    Confidence: import_smithy_client.limitedParseDouble,
    Content: import_smithy_client.expectString,
    EndTime: import_smithy_client.limitedParseDouble,
    StartTime: import_smithy_client.limitedParseDouble,
    Type: import_smithy_client.expectString
  });
}, "de_Entity");
var de_EntityList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_Entity(entry, context);
  });
  return retVal;
}, "de_EntityList");
var de_Item = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Confidence: import_smithy_client.limitedParseDouble,
    Content: import_smithy_client.expectString,
    EndTime: import_smithy_client.limitedParseDouble,
    Speaker: import_smithy_client.expectString,
    Stable: import_smithy_client.expectBoolean,
    StartTime: import_smithy_client.limitedParseDouble,
    Type: import_smithy_client.expectString,
    VocabularyFilterMatch: import_smithy_client.expectBoolean
  });
}, "de_Item");
var de_ItemList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_Item(entry, context);
  });
  return retVal;
}, "de_ItemList");
var de_LanguageIdentification = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_LanguageWithScore(entry, context);
  });
  return retVal;
}, "de_LanguageIdentification");
var de_LanguageWithScore = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    LanguageCode: import_smithy_client.expectString,
    Score: import_smithy_client.limitedParseDouble
  });
}, "de_LanguageWithScore");
var de_MedicalAlternative = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Entities: /* @__PURE__ */ __name((_) => de_MedicalEntityList(_, context), "Entities"),
    Items: /* @__PURE__ */ __name((_) => de_MedicalItemList(_, context), "Items"),
    Transcript: import_smithy_client.expectString
  });
}, "de_MedicalAlternative");
var de_MedicalAlternativeList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_MedicalAlternative(entry, context);
  });
  return retVal;
}, "de_MedicalAlternativeList");
var de_MedicalEntity = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Category: import_smithy_client.expectString,
    Confidence: import_smithy_client.limitedParseDouble,
    Content: import_smithy_client.expectString,
    EndTime: import_smithy_client.limitedParseDouble,
    StartTime: import_smithy_client.limitedParseDouble
  });
}, "de_MedicalEntity");
var de_MedicalEntityList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_MedicalEntity(entry, context);
  });
  return retVal;
}, "de_MedicalEntityList");
var de_MedicalItem = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Confidence: import_smithy_client.limitedParseDouble,
    Content: import_smithy_client.expectString,
    EndTime: import_smithy_client.limitedParseDouble,
    Speaker: import_smithy_client.expectString,
    StartTime: import_smithy_client.limitedParseDouble,
    Type: import_smithy_client.expectString
  });
}, "de_MedicalItem");
var de_MedicalItemList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_MedicalItem(entry, context);
  });
  return retVal;
}, "de_MedicalItemList");
var de_MedicalResult = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Alternatives: /* @__PURE__ */ __name((_) => de_MedicalAlternativeList(_, context), "Alternatives"),
    ChannelId: import_smithy_client.expectString,
    EndTime: import_smithy_client.limitedParseDouble,
    IsPartial: import_smithy_client.expectBoolean,
    ResultId: import_smithy_client.expectString,
    StartTime: import_smithy_client.limitedParseDouble
  });
}, "de_MedicalResult");
var de_MedicalResultList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_MedicalResult(entry, context);
  });
  return retVal;
}, "de_MedicalResultList");
var de_MedicalScribeStreamDetails = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    ChannelDefinitions: import_smithy_client._json,
    EncryptionSettings: import_smithy_client._json,
    LanguageCode: import_smithy_client.expectString,
    MediaEncoding: import_smithy_client.expectString,
    MediaSampleRateHertz: import_smithy_client.expectInt32,
    PostStreamAnalyticsResult: import_smithy_client._json,
    PostStreamAnalyticsSettings: import_smithy_client._json,
    ResourceAccessRoleArn: import_smithy_client.expectString,
    SessionId: import_smithy_client.expectString,
    StreamCreatedAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "StreamCreatedAt"),
    StreamEndedAt: /* @__PURE__ */ __name((_) => (0, import_smithy_client.expectNonNull)((0, import_smithy_client.parseEpochTimestamp)((0, import_smithy_client.expectNumber)(_))), "StreamEndedAt"),
    StreamStatus: import_smithy_client.expectString,
    VocabularyFilterMethod: import_smithy_client.expectString,
    VocabularyFilterName: import_smithy_client.expectString,
    VocabularyName: import_smithy_client.expectString
  });
}, "de_MedicalScribeStreamDetails");
var de_MedicalScribeTranscriptEvent = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    TranscriptSegment: /* @__PURE__ */ __name((_) => de_MedicalScribeTranscriptSegment(_, context), "TranscriptSegment")
  });
}, "de_MedicalScribeTranscriptEvent");
var de_MedicalScribeTranscriptItem = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    BeginAudioTime: import_smithy_client.limitedParseDouble,
    Confidence: import_smithy_client.limitedParseDouble,
    Content: import_smithy_client.expectString,
    EndAudioTime: import_smithy_client.limitedParseDouble,
    Type: import_smithy_client.expectString,
    VocabularyFilterMatch: import_smithy_client.expectBoolean
  });
}, "de_MedicalScribeTranscriptItem");
var de_MedicalScribeTranscriptItemList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_MedicalScribeTranscriptItem(entry, context);
  });
  return retVal;
}, "de_MedicalScribeTranscriptItemList");
var de_MedicalScribeTranscriptSegment = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    BeginAudioTime: import_smithy_client.limitedParseDouble,
    ChannelId: import_smithy_client.expectString,
    Content: import_smithy_client.expectString,
    EndAudioTime: import_smithy_client.limitedParseDouble,
    IsPartial: import_smithy_client.expectBoolean,
    Items: /* @__PURE__ */ __name((_) => de_MedicalScribeTranscriptItemList(_, context), "Items"),
    SegmentId: import_smithy_client.expectString
  });
}, "de_MedicalScribeTranscriptSegment");
var de_MedicalTranscript = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Results: /* @__PURE__ */ __name((_) => de_MedicalResultList(_, context), "Results")
  });
}, "de_MedicalTranscript");
var de_MedicalTranscriptEvent = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Transcript: /* @__PURE__ */ __name((_) => de_MedicalTranscript(_, context), "Transcript")
  });
}, "de_MedicalTranscriptEvent");
var de_Result = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Alternatives: /* @__PURE__ */ __name((_) => de_AlternativeList(_, context), "Alternatives"),
    ChannelId: import_smithy_client.expectString,
    EndTime: import_smithy_client.limitedParseDouble,
    IsPartial: import_smithy_client.expectBoolean,
    LanguageCode: import_smithy_client.expectString,
    LanguageIdentification: /* @__PURE__ */ __name((_) => de_LanguageIdentification(_, context), "LanguageIdentification"),
    ResultId: import_smithy_client.expectString,
    StartTime: import_smithy_client.limitedParseDouble
  });
}, "de_Result");
var de_ResultList = /* @__PURE__ */ __name((output, context) => {
  const retVal = (output || []).filter((e) => e != null).map((entry) => {
    return de_Result(entry, context);
  });
  return retVal;
}, "de_ResultList");
var de_Transcript = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Results: /* @__PURE__ */ __name((_) => de_ResultList(_, context), "Results")
  });
}, "de_Transcript");
var de_TranscriptEvent = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    Transcript: /* @__PURE__ */ __name((_) => de_Transcript(_, context), "Transcript")
  });
}, "de_TranscriptEvent");
var de_UtteranceEvent = /* @__PURE__ */ __name((output, context) => {
  return (0, import_smithy_client.take)(output, {
    BeginOffsetMillis: import_smithy_client.expectLong,
    EndOffsetMillis: import_smithy_client.expectLong,
    Entities: /* @__PURE__ */ __name((_) => de_CallAnalyticsEntityList(_, context), "Entities"),
    IsPartial: import_smithy_client.expectBoolean,
    IssuesDetected: import_smithy_client._json,
    Items: /* @__PURE__ */ __name((_) => de_CallAnalyticsItemList(_, context), "Items"),
    ParticipantRole: import_smithy_client.expectString,
    Sentiment: import_smithy_client.expectString,
    Transcript: import_smithy_client.expectString,
    UtteranceId: import_smithy_client.expectString
  });
}, "de_UtteranceEvent");
var deserializeMetadata = /* @__PURE__ */ __name((output) => ({
  httpStatusCode: output.statusCode,
  requestId: output.headers["x-amzn-requestid"] ?? output.headers["x-amzn-request-id"] ?? output.headers["x-amz-request-id"],
  extendedRequestId: output.headers["x-amz-id-2"],
  cfId: output.headers["x-amz-cf-id"]
}), "deserializeMetadata");
var _CIT = "ContentIdentificationType";
var _CRT = "ContentRedactionType";
var _ECI = "EnableChannelIdentification";
var _EPRS = "EnablePartialResultsStabilization";
var _IL = "IdentifyLanguage";
var _IML = "IdentifyMultipleLanguages";
var _LC = "LanguageCode";
var _LMN = "LanguageModelName";
var _LO = "LanguageOptions";
var _ME = "MediaEncoding";
var _MSRH = "MediaSampleRateHertz";
var _NOC = "NumberOfChannels";
var _PET = "PiiEntityTypes";
var _PL = "PreferredLanguage";
var _PRS = "PartialResultsStability";
var _RI = "RequestId";
var _S = "Specialty";
var _SI = "SessionId";
var _SSL = "ShowSpeakerLabel";
var _T = "Type";
var _VFM = "VocabularyFilterMethod";
var _VFN = "VocabularyFilterName";
var _VFNo = "VocabularyFilterNames";
var _VN = "VocabularyName";
var _VNo = "VocabularyNames";
var _xari = "x-amzn-request-id";
var _xatcit = "x-amzn-transcribe-content-identification-type";
var _xatcrt = "x-amzn-transcribe-content-redaction-type";
var _xateci = "x-amzn-transcribe-enable-channel-identification";
var _xateprs = "x-amzn-transcribe-enable-partial-results-stabilization";
var _xatil = "x-amzn-transcribe-identify-language";
var _xatiml = "x-amzn-transcribe-identify-multiple-languages";
var _xatlc = "x-amzn-transcribe-language-code";
var _xatlmn = "x-amzn-transcribe-language-model-name";
var _xatlo = "x-amzn-transcribe-language-options";
var _xatme = "x-amzn-transcribe-media-encoding";
var _xatnoc = "x-amzn-transcribe-number-of-channels";
var _xatpet = "x-amzn-transcribe-pii-entity-types";
var _xatpl = "x-amzn-transcribe-preferred-language";
var _xatprs = "x-amzn-transcribe-partial-results-stability";
var _xats = "x-amzn-transcribe-specialty";
var _xatsi = "x-amzn-transcribe-session-id";
var _xatsr = "x-amzn-transcribe-sample-rate";
var _xatssl = "x-amzn-transcribe-show-speaker-label";
var _xatt = "x-amzn-transcribe-type";
var _xatvfm = "x-amzn-transcribe-vocabulary-filter-method";
var _xatvfn = "x-amzn-transcribe-vocabulary-filter-name";
var _xatvfn_ = "x-amzn-transcribe-vocabulary-filter-names";
var _xatvn = "x-amzn-transcribe-vocabulary-name";
var _xatvn_ = "x-amzn-transcribe-vocabulary-names";

// src/commands/GetMedicalScribeStreamCommand.ts
var GetMedicalScribeStreamCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions())
  ];
}).s("Transcribe", "GetMedicalScribeStream", {}).n("TranscribeStreamingClient", "GetMedicalScribeStreamCommand").f(void 0, void 0).ser(se_GetMedicalScribeStreamCommand).de(de_GetMedicalScribeStreamCommand).build() {
  static {
    __name(this, "GetMedicalScribeStreamCommand");
  }
};

// src/commands/StartCallAnalyticsStreamTranscriptionCommand.ts

var import_middleware_sdk_transcribe_streaming = require("@aws-sdk/middleware-sdk-transcribe-streaming");




var StartCallAnalyticsStreamTranscriptionCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_eventstream.getEventStreamPlugin)(config),
    (0, import_middleware_websocket.getWebSocketPlugin)(config, {
      headerPrefix: "x-amzn-transcribe-"
    }),
    (0, import_middleware_sdk_transcribe_streaming.getTranscribeStreamingPlugin)(config)
  ];
}).s("Transcribe", "StartCallAnalyticsStreamTranscription", {
  /**
   * @internal
   */
  eventStream: {
    input: true,
    output: true
  }
}).n("TranscribeStreamingClient", "StartCallAnalyticsStreamTranscriptionCommand").f(
  StartCallAnalyticsStreamTranscriptionRequestFilterSensitiveLog,
  StartCallAnalyticsStreamTranscriptionResponseFilterSensitiveLog
).ser(se_StartCallAnalyticsStreamTranscriptionCommand).de(de_StartCallAnalyticsStreamTranscriptionCommand).build() {
  static {
    __name(this, "StartCallAnalyticsStreamTranscriptionCommand");
  }
};

// src/commands/StartMedicalScribeStreamCommand.ts






var StartMedicalScribeStreamCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_eventstream.getEventStreamPlugin)(config),
    (0, import_middleware_websocket.getWebSocketPlugin)(config, {
      headerPrefix: "x-amzn-transcribe-"
    }),
    (0, import_middleware_sdk_transcribe_streaming.getTranscribeStreamingPlugin)(config)
  ];
}).s("Transcribe", "StartMedicalScribeStream", {
  /**
   * @internal
   */
  eventStream: {
    input: true,
    output: true
  }
}).n("TranscribeStreamingClient", "StartMedicalScribeStreamCommand").f(StartMedicalScribeStreamRequestFilterSensitiveLog, StartMedicalScribeStreamResponseFilterSensitiveLog).ser(se_StartMedicalScribeStreamCommand).de(de_StartMedicalScribeStreamCommand).build() {
  static {
    __name(this, "StartMedicalScribeStreamCommand");
  }
};

// src/commands/StartMedicalStreamTranscriptionCommand.ts






var StartMedicalStreamTranscriptionCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_eventstream.getEventStreamPlugin)(config),
    (0, import_middleware_websocket.getWebSocketPlugin)(config, {
      headerPrefix: "x-amzn-transcribe-"
    }),
    (0, import_middleware_sdk_transcribe_streaming.getTranscribeStreamingPlugin)(config)
  ];
}).s("Transcribe", "StartMedicalStreamTranscription", {
  /**
   * @internal
   */
  eventStream: {
    input: true,
    output: true
  }
}).n("TranscribeStreamingClient", "StartMedicalStreamTranscriptionCommand").f(
  StartMedicalStreamTranscriptionRequestFilterSensitiveLog,
  StartMedicalStreamTranscriptionResponseFilterSensitiveLog
).ser(se_StartMedicalStreamTranscriptionCommand).de(de_StartMedicalStreamTranscriptionCommand).build() {
  static {
    __name(this, "StartMedicalStreamTranscriptionCommand");
  }
};

// src/commands/StartStreamTranscriptionCommand.ts






var StartStreamTranscriptionCommand = class extends import_smithy_client.Command.classBuilder().ep(commonParams).m(function(Command, cs, config, o) {
  return [
    (0, import_middleware_serde.getSerdePlugin)(config, this.serialize, this.deserialize),
    (0, import_middleware_endpoint.getEndpointPlugin)(config, Command.getEndpointParameterInstructions()),
    (0, import_middleware_eventstream.getEventStreamPlugin)(config),
    (0, import_middleware_websocket.getWebSocketPlugin)(config, {
      headerPrefix: "x-amzn-transcribe-"
    }),
    (0, import_middleware_sdk_transcribe_streaming.getTranscribeStreamingPlugin)(config)
  ];
}).s("Transcribe", "StartStreamTranscription", {
  /**
   * @internal
   */
  eventStream: {
    input: true,
    output: true
  }
}).n("TranscribeStreamingClient", "StartStreamTranscriptionCommand").f(StartStreamTranscriptionRequestFilterSensitiveLog, StartStreamTranscriptionResponseFilterSensitiveLog).ser(se_StartStreamTranscriptionCommand).de(de_StartStreamTranscriptionCommand).build() {
  static {
    __name(this, "StartStreamTranscriptionCommand");
  }
};

// src/TranscribeStreaming.ts
var commands = {
  GetMedicalScribeStreamCommand,
  StartCallAnalyticsStreamTranscriptionCommand,
  StartMedicalScribeStreamCommand,
  StartMedicalStreamTranscriptionCommand,
  StartStreamTranscriptionCommand
};
var TranscribeStreaming = class extends TranscribeStreamingClient {
  static {
    __name(this, "TranscribeStreaming");
  }
};
(0, import_smithy_client.createAggregatedClient)(commands, TranscribeStreaming);
// Annotate the CommonJS export names for ESM import in node:

0 && (module.exports = {
  TranscribeStreamingServiceException,
  __Client,
  TranscribeStreamingClient,
  TranscribeStreaming,
  $Command,
  GetMedicalScribeStreamCommand,
  StartCallAnalyticsStreamTranscriptionCommand,
  StartMedicalScribeStreamCommand,
  StartMedicalStreamTranscriptionCommand,
  StartStreamTranscriptionCommand,
  ItemType,
  ParticipantRole,
  ContentRedactionOutput,
  AudioStream,
  BadRequestException,
  CallAnalyticsLanguageCode,
  ConflictException,
  InternalFailureException,
  LimitExceededException,
  ServiceUnavailableException,
  Sentiment,
  CallAnalyticsTranscriptResultStream,
  ClinicalNoteGenerationStatus,
  MedicalScribeNoteTemplate,
  ContentIdentificationType,
  ContentRedactionType,
  MedicalScribeParticipantRole,
  MedicalScribeLanguageCode,
  MedicalScribeMediaEncoding,
  MedicalScribeStreamStatus,
  MedicalScribeVocabularyFilterMethod,
  ResourceNotFoundException,
  LanguageCode,
  MediaEncoding,
  MedicalContentIdentificationType,
  MedicalScribeSessionControlEventType,
  MedicalScribeInputStream,
  MedicalScribeTranscriptItemType,
  MedicalScribeResultStream,
  MedicalTranscriptResultStream,
  PartialResultsStability,
  Specialty,
  VocabularyFilterMethod,
  Type,
  TranscriptResultStream,
  AudioStreamFilterSensitiveLog,
  CallAnalyticsTranscriptResultStreamFilterSensitiveLog,
  MedicalScribeInputStreamFilterSensitiveLog,
  MedicalScribeResultStreamFilterSensitiveLog,
  MedicalTranscriptResultStreamFilterSensitiveLog,
  StartCallAnalyticsStreamTranscriptionRequestFilterSensitiveLog,
  StartCallAnalyticsStreamTranscriptionResponseFilterSensitiveLog,
  StartMedicalScribeStreamRequestFilterSensitiveLog,
  StartMedicalScribeStreamResponseFilterSensitiveLog,
  StartMedicalStreamTranscriptionRequestFilterSensitiveLog,
  StartMedicalStreamTranscriptionResponseFilterSensitiveLog,
  StartStreamTranscriptionRequestFilterSensitiveLog,
  TranscriptResultStreamFilterSensitiveLog,
  StartStreamTranscriptionResponseFilterSensitiveLog
});

