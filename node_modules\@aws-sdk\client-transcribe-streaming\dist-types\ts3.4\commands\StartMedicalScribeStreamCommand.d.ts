import { Command as $Command } from "@smithy/smithy-client";
import { Metada<PERSON>Bearer as __MetadataBearer } from "@smithy/types";
import {
  StartMedicalScribeStreamRequest,
  StartMedicalScribeStreamResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  TranscribeStreamingClientResolvedConfig,
} from "../TranscribeStreamingClient";
export { __MetadataBearer };
export { $Command };
export interface StartMedicalScribeStreamCommandInput
  extends StartMedicalScribeStreamRequest {}
export interface StartMedicalScribeStreamCommandOutput
  extends StartMedicalScribeStreamResponse,
    __MetadataBearer {}
declare const StartMedicalScribeStreamCommand_base: {
  new (
    input: StartMedicalScribeStreamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartMedicalScribeStreamCommandInput,
    StartMedicalScribeStreamCommandOutput,
    TranscribeStreamingClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: StartMedicalScribeStreamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    StartMedicalScribeStreamCommandInput,
    StartMedicalScribeStreamCommandOutput,
    TranscribeStreamingClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class StartMedicalScribeStreamCommand extends StartMedicalScribeStreamCommand_base {
  protected static __types: {
    api: {
      input: StartMedicalScribeStreamRequest;
      output: StartMedicalScribeStreamResponse;
    };
    sdk: {
      input: StartMedicalScribeStreamCommandInput;
      output: StartMedicalScribeStreamCommandOutput;
    };
  };
}
