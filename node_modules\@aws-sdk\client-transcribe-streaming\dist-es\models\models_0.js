import { TranscribeStreamingServiceException as __BaseException } from "./TranscribeStreamingServiceException";
export const ItemType = {
    PRONUNCIATION: "pronunciation",
    PUNCTUATION: "punctuation",
};
export const ParticipantRole = {
    AGENT: "AGENT",
    CUSTOMER: "CUSTOMER",
};
export const ContentRedactionOutput = {
    REDACTED: "redacted",
    REDACTED_AND_UNREDACTED: "redacted_and_unredacted",
};
export var AudioStream;
(function (AudioStream) {
    AudioStream.visit = (value, visitor) => {
        if (value.AudioEvent !== undefined)
            return visitor.AudioEvent(value.AudioEvent);
        if (value.ConfigurationEvent !== undefined)
            return visitor.ConfigurationEvent(value.ConfigurationEvent);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(AudioStream || (AudioStream = {}));
export class BadRequestException extends __BaseException {
    name = "BadRequestException";
    $fault = "client";
    Message;
    constructor(opts) {
        super({
            name: "BadRequestException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, BadRequestException.prototype);
        this.Message = opts.Message;
    }
}
export const CallAnalyticsLanguageCode = {
    DE_DE: "de-DE",
    EN_AU: "en-AU",
    EN_GB: "en-GB",
    EN_US: "en-US",
    ES_US: "es-US",
    FR_CA: "fr-CA",
    FR_FR: "fr-FR",
    IT_IT: "it-IT",
    PT_BR: "pt-BR",
};
export class ConflictException extends __BaseException {
    name = "ConflictException";
    $fault = "client";
    Message;
    constructor(opts) {
        super({
            name: "ConflictException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ConflictException.prototype);
        this.Message = opts.Message;
    }
}
export class InternalFailureException extends __BaseException {
    name = "InternalFailureException";
    $fault = "server";
    Message;
    constructor(opts) {
        super({
            name: "InternalFailureException",
            $fault: "server",
            ...opts,
        });
        Object.setPrototypeOf(this, InternalFailureException.prototype);
        this.Message = opts.Message;
    }
}
export class LimitExceededException extends __BaseException {
    name = "LimitExceededException";
    $fault = "client";
    Message;
    constructor(opts) {
        super({
            name: "LimitExceededException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, LimitExceededException.prototype);
        this.Message = opts.Message;
    }
}
export class ServiceUnavailableException extends __BaseException {
    name = "ServiceUnavailableException";
    $fault = "server";
    Message;
    constructor(opts) {
        super({
            name: "ServiceUnavailableException",
            $fault: "server",
            ...opts,
        });
        Object.setPrototypeOf(this, ServiceUnavailableException.prototype);
        this.Message = opts.Message;
    }
}
export const Sentiment = {
    MIXED: "MIXED",
    NEGATIVE: "NEGATIVE",
    NEUTRAL: "NEUTRAL",
    POSITIVE: "POSITIVE",
};
export var CallAnalyticsTranscriptResultStream;
(function (CallAnalyticsTranscriptResultStream) {
    CallAnalyticsTranscriptResultStream.visit = (value, visitor) => {
        if (value.UtteranceEvent !== undefined)
            return visitor.UtteranceEvent(value.UtteranceEvent);
        if (value.CategoryEvent !== undefined)
            return visitor.CategoryEvent(value.CategoryEvent);
        if (value.BadRequestException !== undefined)
            return visitor.BadRequestException(value.BadRequestException);
        if (value.LimitExceededException !== undefined)
            return visitor.LimitExceededException(value.LimitExceededException);
        if (value.InternalFailureException !== undefined)
            return visitor.InternalFailureException(value.InternalFailureException);
        if (value.ConflictException !== undefined)
            return visitor.ConflictException(value.ConflictException);
        if (value.ServiceUnavailableException !== undefined)
            return visitor.ServiceUnavailableException(value.ServiceUnavailableException);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(CallAnalyticsTranscriptResultStream || (CallAnalyticsTranscriptResultStream = {}));
export const ClinicalNoteGenerationStatus = {
    COMPLETED: "COMPLETED",
    FAILED: "FAILED",
    IN_PROGRESS: "IN_PROGRESS",
};
export const MedicalScribeNoteTemplate = {
    BEHAVIORAL_SOAP: "BEHAVIORAL_SOAP",
    BIRP: "BIRP",
    DAP: "DAP",
    GIRPP: "GIRPP",
    HISTORY_AND_PHYSICAL: "HISTORY_AND_PHYSICAL",
    PHYSICAL_SOAP: "PHYSICAL_SOAP",
    SIRP: "SIRP",
};
export const ContentIdentificationType = {
    PII: "PII",
};
export const ContentRedactionType = {
    PII: "PII",
};
export const MedicalScribeParticipantRole = {
    CLINICIAN: "CLINICIAN",
    PATIENT: "PATIENT",
};
export const MedicalScribeLanguageCode = {
    EN_US: "en-US",
};
export const MedicalScribeMediaEncoding = {
    FLAC: "flac",
    OGG_OPUS: "ogg-opus",
    PCM: "pcm",
};
export const MedicalScribeStreamStatus = {
    COMPLETED: "COMPLETED",
    FAILED: "FAILED",
    IN_PROGRESS: "IN_PROGRESS",
    PAUSED: "PAUSED",
};
export const MedicalScribeVocabularyFilterMethod = {
    MASK: "mask",
    REMOVE: "remove",
    TAG: "tag",
};
export class ResourceNotFoundException extends __BaseException {
    name = "ResourceNotFoundException";
    $fault = "client";
    Message;
    constructor(opts) {
        super({
            name: "ResourceNotFoundException",
            $fault: "client",
            ...opts,
        });
        Object.setPrototypeOf(this, ResourceNotFoundException.prototype);
        this.Message = opts.Message;
    }
}
export const LanguageCode = {
    AF_ZA: "af-ZA",
    AR_AE: "ar-AE",
    AR_SA: "ar-SA",
    CA_ES: "ca-ES",
    CS_CZ: "cs-CZ",
    DA_DK: "da-DK",
    DE_CH: "de-CH",
    DE_DE: "de-DE",
    EL_GR: "el-GR",
    EN_AB: "en-AB",
    EN_AU: "en-AU",
    EN_GB: "en-GB",
    EN_IE: "en-IE",
    EN_IN: "en-IN",
    EN_NZ: "en-NZ",
    EN_US: "en-US",
    EN_WL: "en-WL",
    EN_ZA: "en-ZA",
    ES_ES: "es-ES",
    ES_US: "es-US",
    EU_ES: "eu-ES",
    FA_IR: "fa-IR",
    FI_FI: "fi-FI",
    FR_CA: "fr-CA",
    FR_FR: "fr-FR",
    GL_ES: "gl-ES",
    HE_IL: "he-IL",
    HI_IN: "hi-IN",
    HR_HR: "hr-HR",
    ID_ID: "id-ID",
    IT_IT: "it-IT",
    JA_JP: "ja-JP",
    KO_KR: "ko-KR",
    LV_LV: "lv-LV",
    MS_MY: "ms-MY",
    NL_NL: "nl-NL",
    NO_NO: "no-NO",
    PL_PL: "pl-PL",
    PT_BR: "pt-BR",
    PT_PT: "pt-PT",
    RO_RO: "ro-RO",
    RU_RU: "ru-RU",
    SK_SK: "sk-SK",
    SO_SO: "so-SO",
    SR_RS: "sr-RS",
    SV_SE: "sv-SE",
    TH_TH: "th-TH",
    TL_PH: "tl-PH",
    UK_UA: "uk-UA",
    VI_VN: "vi-VN",
    ZH_CN: "zh-CN",
    ZH_HK: "zh-HK",
    ZH_TW: "zh-TW",
    ZU_ZA: "zu-ZA",
};
export const MediaEncoding = {
    FLAC: "flac",
    OGG_OPUS: "ogg-opus",
    PCM: "pcm",
};
export const MedicalContentIdentificationType = {
    PHI: "PHI",
};
export const MedicalScribeSessionControlEventType = {
    END_OF_SESSION: "END_OF_SESSION",
};
export var MedicalScribeInputStream;
(function (MedicalScribeInputStream) {
    MedicalScribeInputStream.visit = (value, visitor) => {
        if (value.AudioEvent !== undefined)
            return visitor.AudioEvent(value.AudioEvent);
        if (value.SessionControlEvent !== undefined)
            return visitor.SessionControlEvent(value.SessionControlEvent);
        if (value.ConfigurationEvent !== undefined)
            return visitor.ConfigurationEvent(value.ConfigurationEvent);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(MedicalScribeInputStream || (MedicalScribeInputStream = {}));
export const MedicalScribeTranscriptItemType = {
    PRONUNCIATION: "pronunciation",
    PUNCTUATION: "punctuation",
};
export var MedicalScribeResultStream;
(function (MedicalScribeResultStream) {
    MedicalScribeResultStream.visit = (value, visitor) => {
        if (value.TranscriptEvent !== undefined)
            return visitor.TranscriptEvent(value.TranscriptEvent);
        if (value.BadRequestException !== undefined)
            return visitor.BadRequestException(value.BadRequestException);
        if (value.LimitExceededException !== undefined)
            return visitor.LimitExceededException(value.LimitExceededException);
        if (value.InternalFailureException !== undefined)
            return visitor.InternalFailureException(value.InternalFailureException);
        if (value.ConflictException !== undefined)
            return visitor.ConflictException(value.ConflictException);
        if (value.ServiceUnavailableException !== undefined)
            return visitor.ServiceUnavailableException(value.ServiceUnavailableException);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(MedicalScribeResultStream || (MedicalScribeResultStream = {}));
export var MedicalTranscriptResultStream;
(function (MedicalTranscriptResultStream) {
    MedicalTranscriptResultStream.visit = (value, visitor) => {
        if (value.TranscriptEvent !== undefined)
            return visitor.TranscriptEvent(value.TranscriptEvent);
        if (value.BadRequestException !== undefined)
            return visitor.BadRequestException(value.BadRequestException);
        if (value.LimitExceededException !== undefined)
            return visitor.LimitExceededException(value.LimitExceededException);
        if (value.InternalFailureException !== undefined)
            return visitor.InternalFailureException(value.InternalFailureException);
        if (value.ConflictException !== undefined)
            return visitor.ConflictException(value.ConflictException);
        if (value.ServiceUnavailableException !== undefined)
            return visitor.ServiceUnavailableException(value.ServiceUnavailableException);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(MedicalTranscriptResultStream || (MedicalTranscriptResultStream = {}));
export const PartialResultsStability = {
    HIGH: "high",
    LOW: "low",
    MEDIUM: "medium",
};
export const Specialty = {
    CARDIOLOGY: "CARDIOLOGY",
    NEUROLOGY: "NEUROLOGY",
    ONCOLOGY: "ONCOLOGY",
    PRIMARYCARE: "PRIMARYCARE",
    RADIOLOGY: "RADIOLOGY",
    UROLOGY: "UROLOGY",
};
export const VocabularyFilterMethod = {
    MASK: "mask",
    REMOVE: "remove",
    TAG: "tag",
};
export const Type = {
    CONVERSATION: "CONVERSATION",
    DICTATION: "DICTATION",
};
export var TranscriptResultStream;
(function (TranscriptResultStream) {
    TranscriptResultStream.visit = (value, visitor) => {
        if (value.TranscriptEvent !== undefined)
            return visitor.TranscriptEvent(value.TranscriptEvent);
        if (value.BadRequestException !== undefined)
            return visitor.BadRequestException(value.BadRequestException);
        if (value.LimitExceededException !== undefined)
            return visitor.LimitExceededException(value.LimitExceededException);
        if (value.InternalFailureException !== undefined)
            return visitor.InternalFailureException(value.InternalFailureException);
        if (value.ConflictException !== undefined)
            return visitor.ConflictException(value.ConflictException);
        if (value.ServiceUnavailableException !== undefined)
            return visitor.ServiceUnavailableException(value.ServiceUnavailableException);
        return visitor._(value.$unknown[0], value.$unknown[1]);
    };
})(TranscriptResultStream || (TranscriptResultStream = {}));
export const AudioStreamFilterSensitiveLog = (obj) => {
    if (obj.AudioEvent !== undefined)
        return { AudioEvent: obj.AudioEvent };
    if (obj.ConfigurationEvent !== undefined)
        return { ConfigurationEvent: obj.ConfigurationEvent };
    if (obj.$unknown !== undefined)
        return { [obj.$unknown[0]]: "UNKNOWN" };
};
export const CallAnalyticsTranscriptResultStreamFilterSensitiveLog = (obj) => {
    if (obj.UtteranceEvent !== undefined)
        return { UtteranceEvent: obj.UtteranceEvent };
    if (obj.CategoryEvent !== undefined)
        return { CategoryEvent: obj.CategoryEvent };
    if (obj.BadRequestException !== undefined)
        return { BadRequestException: obj.BadRequestException };
    if (obj.LimitExceededException !== undefined)
        return { LimitExceededException: obj.LimitExceededException };
    if (obj.InternalFailureException !== undefined)
        return { InternalFailureException: obj.InternalFailureException };
    if (obj.ConflictException !== undefined)
        return { ConflictException: obj.ConflictException };
    if (obj.ServiceUnavailableException !== undefined)
        return { ServiceUnavailableException: obj.ServiceUnavailableException };
    if (obj.$unknown !== undefined)
        return { [obj.$unknown[0]]: "UNKNOWN" };
};
export const MedicalScribeInputStreamFilterSensitiveLog = (obj) => {
    if (obj.AudioEvent !== undefined)
        return { AudioEvent: obj.AudioEvent };
    if (obj.SessionControlEvent !== undefined)
        return { SessionControlEvent: obj.SessionControlEvent };
    if (obj.ConfigurationEvent !== undefined)
        return { ConfigurationEvent: obj.ConfigurationEvent };
    if (obj.$unknown !== undefined)
        return { [obj.$unknown[0]]: "UNKNOWN" };
};
export const MedicalScribeResultStreamFilterSensitiveLog = (obj) => {
    if (obj.TranscriptEvent !== undefined)
        return { TranscriptEvent: obj.TranscriptEvent };
    if (obj.BadRequestException !== undefined)
        return { BadRequestException: obj.BadRequestException };
    if (obj.LimitExceededException !== undefined)
        return { LimitExceededException: obj.LimitExceededException };
    if (obj.InternalFailureException !== undefined)
        return { InternalFailureException: obj.InternalFailureException };
    if (obj.ConflictException !== undefined)
        return { ConflictException: obj.ConflictException };
    if (obj.ServiceUnavailableException !== undefined)
        return { ServiceUnavailableException: obj.ServiceUnavailableException };
    if (obj.$unknown !== undefined)
        return { [obj.$unknown[0]]: "UNKNOWN" };
};
export const MedicalTranscriptResultStreamFilterSensitiveLog = (obj) => {
    if (obj.TranscriptEvent !== undefined)
        return { TranscriptEvent: obj.TranscriptEvent };
    if (obj.BadRequestException !== undefined)
        return { BadRequestException: obj.BadRequestException };
    if (obj.LimitExceededException !== undefined)
        return { LimitExceededException: obj.LimitExceededException };
    if (obj.InternalFailureException !== undefined)
        return { InternalFailureException: obj.InternalFailureException };
    if (obj.ConflictException !== undefined)
        return { ConflictException: obj.ConflictException };
    if (obj.ServiceUnavailableException !== undefined)
        return { ServiceUnavailableException: obj.ServiceUnavailableException };
    if (obj.$unknown !== undefined)
        return { [obj.$unknown[0]]: "UNKNOWN" };
};
export const StartCallAnalyticsStreamTranscriptionRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.AudioStream && { AudioStream: "STREAMING_CONTENT" }),
});
export const StartCallAnalyticsStreamTranscriptionResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.CallAnalyticsTranscriptResultStream && { CallAnalyticsTranscriptResultStream: "STREAMING_CONTENT" }),
});
export const StartMedicalScribeStreamRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.InputStream && { InputStream: "STREAMING_CONTENT" }),
});
export const StartMedicalScribeStreamResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.ResultStream && { ResultStream: "STREAMING_CONTENT" }),
});
export const StartMedicalStreamTranscriptionRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.AudioStream && { AudioStream: "STREAMING_CONTENT" }),
});
export const StartMedicalStreamTranscriptionResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.TranscriptResultStream && { TranscriptResultStream: "STREAMING_CONTENT" }),
});
export const StartStreamTranscriptionRequestFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.AudioStream && { AudioStream: "STREAMING_CONTENT" }),
});
export const TranscriptResultStreamFilterSensitiveLog = (obj) => {
    if (obj.TranscriptEvent !== undefined)
        return { TranscriptEvent: obj.TranscriptEvent };
    if (obj.BadRequestException !== undefined)
        return { BadRequestException: obj.BadRequestException };
    if (obj.LimitExceededException !== undefined)
        return { LimitExceededException: obj.LimitExceededException };
    if (obj.InternalFailureException !== undefined)
        return { InternalFailureException: obj.InternalFailureException };
    if (obj.ConflictException !== undefined)
        return { ConflictException: obj.ConflictException };
    if (obj.ServiceUnavailableException !== undefined)
        return { ServiceUnavailableException: obj.ServiceUnavailableException };
    if (obj.$unknown !== undefined)
        return { [obj.$unknown[0]]: "UNKNOWN" };
};
export const StartStreamTranscriptionResponseFilterSensitiveLog = (obj) => ({
    ...obj,
    ...(obj.TranscriptResultStream && { TranscriptResultStream: "STREAMING_CONTENT" }),
});
