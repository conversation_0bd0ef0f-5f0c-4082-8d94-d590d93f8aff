{"name": "example-javascriptv3-get-started-node", "version": "1.0.0", "description": "This guide shows you how to initialize an NPM package, add a service client to your package, and use the JavaScript SDK to call a service action.", "main": "index.js", "scripts": {"test": "vitest run **/*.unit.test.js"}, "author": "Your Name", "license": "Apache-2.0", "dependencies": {"@aws-sdk/client-s3": "^3.420.0", "@aws-sdk/client-transcribe-streaming": "^3.844.0"}}