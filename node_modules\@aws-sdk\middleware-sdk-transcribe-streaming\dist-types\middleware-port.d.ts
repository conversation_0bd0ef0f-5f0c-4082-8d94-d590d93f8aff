import { BuildMiddleware, RelativeMiddlewareOptions, RequestHandler } from "@smithy/types";
/**
 * Middleware that generates WebSocket URL to TranscribeStreaming service
 * Reference: https://docs.aws.amazon.com/transcribe/latest/dg/websocket.html
 */
export declare const websocketPortMiddleware: (options: {
    requestHandler: RequestHandler<any, any>;
}) => BuildMiddleware<any, any>;
export declare const websocketPortMiddlewareOptions: RelativeMiddlewareOptions;
