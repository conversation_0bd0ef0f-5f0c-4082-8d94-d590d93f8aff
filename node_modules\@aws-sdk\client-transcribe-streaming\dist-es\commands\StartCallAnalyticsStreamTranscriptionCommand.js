import { getEventStreamPlugin } from "@aws-sdk/middleware-eventstream";
import { getTranscribeStreamingPlugin } from "@aws-sdk/middleware-sdk-transcribe-streaming";
import { getWebSocketPlugin } from "@aws-sdk/middleware-websocket";
import { getEndpointPlugin } from "@smithy/middleware-endpoint";
import { getSerdePlugin } from "@smithy/middleware-serde";
import { Command as $Command } from "@smithy/smithy-client";
import { commonParams } from "../endpoint/EndpointParameters";
import { StartCallAnalyticsStreamTranscriptionRequestFilterSensitiveLog, StartCallAnalyticsStreamTranscriptionResponseFilterSensitiveLog, } from "../models/models_0";
import { de_StartCallAnalyticsStreamTranscriptionCommand, se_StartCallAnalyticsStreamTranscriptionCommand, } from "../protocols/Aws_restJson1";
export { $Command };
export class StartCallAnalyticsStreamTranscriptionCommand extends $Command
    .classBuilder()
    .ep(commonParams)
    .m(function (Command, cs, config, o) {
    return [
        getSerdePlugin(config, this.serialize, this.deserialize),
        getEndpointPlugin(config, Command.getEndpointParameterInstructions()),
        getEventStreamPlugin(config),
        getWebSocketPlugin(config, {
            headerPrefix: "x-amzn-transcribe-",
        }),
        getTranscribeStreamingPlugin(config),
    ];
})
    .s("Transcribe", "StartCallAnalyticsStreamTranscription", {
    eventStream: {
        input: true,
        output: true,
    },
})
    .n("TranscribeStreamingClient", "StartCallAnalyticsStreamTranscriptionCommand")
    .f(StartCallAnalyticsStreamTranscriptionRequestFilterSensitiveLog, StartCallAnalyticsStreamTranscriptionResponseFilterSensitiveLog)
    .ser(se_StartCallAnalyticsStreamTranscriptionCommand)
    .de(de_StartCallAnalyticsStreamTranscriptionCommand)
    .build() {
}
