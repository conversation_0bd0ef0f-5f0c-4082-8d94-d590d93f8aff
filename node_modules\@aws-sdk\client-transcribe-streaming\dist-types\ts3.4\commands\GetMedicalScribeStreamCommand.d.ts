import { Command as $Command } from "@smithy/smithy-client";
import { MetadataBearer as __MetadataBearer } from "@smithy/types";
import {
  GetMedicalScribeStreamRequest,
  GetMedicalScribeStreamResponse,
} from "../models/models_0";
import {
  ServiceInputTypes,
  ServiceOutputTypes,
  TranscribeStreamingClientResolvedConfig,
} from "../TranscribeStreamingClient";
export { __MetadataBearer };
export { $Command };
export interface GetMedicalScribeStreamCommandInput
  extends GetMedicalScribeStreamRequest {}
export interface GetMedicalScribeStreamCommandOutput
  extends GetMedicalScribeStreamResponse,
    __MetadataBearer {}
declare const GetMedicalScribeStreamCommand_base: {
  new (
    input: GetMedicalScribeStreamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetMedicalScribeStreamCommandInput,
    GetMedicalScribeStreamCommandOutput,
    TranscribeStreamingClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  new (
    input: GetMedicalScribeStreamCommandInput
  ): import("@smithy/smithy-client").CommandImpl<
    GetMedicalScribeStreamCommandInput,
    GetMedicalScribeStreamCommandOutput,
    TranscribeStreamingClientResolvedConfig,
    ServiceInputTypes,
    ServiceOutputTypes
  >;
  getEndpointParameterInstructions(): import("@smithy/middleware-endpoint").EndpointParameterInstructions;
};
export declare class GetMedicalScribeStreamCommand extends GetMedicalScribeStreamCommand_base {
  protected static __types: {
    api: {
      input: GetMedicalScribeStreamRequest;
      output: GetMedicalScribeStreamResponse;
    };
    sdk: {
      input: GetMedicalScribeStreamCommandInput;
      output: GetMedicalScribeStreamCommandOutput;
    };
  };
}
